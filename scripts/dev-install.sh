#!/bin/bash

# Development installation script for nest-postman-sync
# This script builds, packages, and installs the CLI tool in the target NestJS project

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CLI_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
TARGET_PROJECT="$HOME/Desktop/tk-lpm-backend"
PACKAGE_NAME="nest-postman-sync"

echo -e "${BLUE}🔧 Starting development installation workflow...${NC}\n"

# Step 1: Clean and build the project
echo -e "${YELLOW}📦 Step 1: Cleaning and building the CLI tool...${NC}"
cd "$CLI_DIR"
echo -e "${BLUE}🧹 Cleaning old build files...${NC}"
npm run clean
echo -e "${BLUE}🔨 Building fresh...${NC}"
npm run build
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Build completed successfully${NC}\n"
else
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

# Step 2: Create package tarball
echo -e "${YELLOW}📦 Step 2: Creating package tarball...${NC}"
npm pack
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Package created successfully${NC}\n"
else
    echo -e "${RED}❌ Package creation failed${NC}"
    exit 1
fi

# Get the generated tarball name
TARBALL=$(ls -t ${PACKAGE_NAME}-*.tgz | head -n1)
echo -e "${BLUE}📄 Generated tarball: ${TARBALL}${NC}\n"

# Step 3: Navigate to target project
echo -e "${YELLOW}📁 Step 3: Navigating to target project...${NC}"
if [ ! -d "$TARGET_PROJECT" ]; then
    echo -e "${RED}❌ Target project directory not found: $TARGET_PROJECT${NC}"
    exit 1
fi

cd "$TARGET_PROJECT"
echo -e "${GREEN}✅ Changed to target project directory${NC}\n"

# Step 4: Uninstall existing package
echo -e "${YELLOW}🗑️  Step 4: Uninstalling existing package...${NC}"
if npm_execpath="yarn" yarn list --depth=0 | grep -q "$PACKAGE_NAME"; then
    npm_execpath="yarn" yarn remove "$PACKAGE_NAME"
    echo -e "${GREEN}✅ Existing package removed${NC}\n"
else
    echo -e "${BLUE}ℹ️  No existing package found to remove${NC}\n"
fi

# Step 5: Install new package
echo -e "${YELLOW}⬇️  Step 5: Installing new package...${NC}"
# Temporarily disable the npm check by setting the environment variable
npm_execpath="yarn" yarn add "../nest-postman-sync/$TARBALL" --dev
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Package installed successfully${NC}\n"
else
    echo -e "${RED}❌ Package installation failed${NC}"
    exit 1
fi

# Step 6: Verify installation
echo -e "${YELLOW}🔍 Step 6: Verifying installation...${NC}"
if npx nest-postman-sync --version > /dev/null 2>&1; then
    VERSION=$(npx nest-postman-sync --version)
    echo -e "${GREEN}✅ Installation verified - Version: $VERSION${NC}\n"
else
    echo -e "${RED}❌ Installation verification failed${NC}"
    exit 1
fi

# Step 7: Clean up old tarballs (keep only the latest 3)
echo -e "${YELLOW}🧹 Step 7: Cleaning up old tarballs...${NC}"
cd "$CLI_DIR"
ls -t ${PACKAGE_NAME}-*.tgz | tail -n +4 | xargs -r rm
echo -e "${GREEN}✅ Cleanup completed${NC}\n"

echo -e "${GREEN}🎉 Development installation completed successfully!${NC}"
echo -e "${BLUE}💡 You can now test the CLI tool in the target project:${NC}"
echo -e "   cd $TARGET_PROJECT"
echo -e "   npx nest-postman-sync --help"
echo -e "   npx nest-postman-sync generate"
echo ""
