#!/usr/bin/env node

/**
 * Installation verification script
 * Tests that the CLI works correctly after installation
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying nest-postman-sync installation...\n');

// Test 1: Check if the CLI is accessible
try {
  console.log('✅ Testing CLI accessibility...');
  const version = execSync('nest-postman-sync --version', { encoding: 'utf8' }).trim();
  console.log(`   Version: ${version}`);
} catch (error) {
  console.error('❌ CLI not accessible');
  console.error('   Make sure nest-postman-sync is installed globally or use npx/yarn');
  process.exit(1);
}

// Test 2: Check help command
try {
  console.log('✅ Testing help command...');
  execSync('nest-postman-sync --help', { stdio: 'pipe' });
  console.log('   Help command works');
} catch (error) {
  console.error('❌ Help command failed');
  process.exit(1);
}

// Test 3: Check if all commands are available
const commands = ['init', 'generate', 'sync', 'watch'];
for (const command of commands) {
  try {
    console.log(`✅ Testing ${command} command help...`);
    execSync(`nest-postman-sync ${command} --help`, { stdio: 'pipe' });
    console.log(`   ${command} command available`);
  } catch (error) {
    console.error(`❌ ${command} command not available`);
    process.exit(1);
  }
}

// Test 4: Check package.json bin configuration
try {
  console.log('✅ Checking package.json configuration...');
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '..', 'package.json'), 'utf8'));
  
  if (!packageJson.bin || !packageJson.bin['nest-postman-sync']) {
    throw new Error('bin configuration missing');
  }
  
  const binPath = path.join(__dirname, '..', packageJson.bin['nest-postman-sync']);
  if (!fs.existsSync(binPath)) {
    throw new Error(`Binary file not found: ${binPath}`);
  }
  
  console.log('   package.json bin configuration correct');
  console.log(`   Binary path: ${packageJson.bin['nest-postman-sync']}`);
} catch (error) {
  console.error('❌ Package configuration issue:', error.message);
  process.exit(1);
}

console.log('\n🎉 All installation verification tests passed!');
console.log('\n📋 Installation Summary:');
console.log('   ✅ CLI is accessible');
console.log('   ✅ All commands available');
console.log('   ✅ Package configuration correct');
console.log('\n💡 Next steps:');
console.log('   1. Run: nest-postman-sync init');
console.log('   2. Configure your Postman API key');
console.log('   3. Generate your first collection');
console.log('\n📚 Documentation: https://github.com/your-org/nest-postman-sync#readme');
