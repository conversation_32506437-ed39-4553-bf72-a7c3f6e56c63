import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';

export class CreateUserDto {
  name: string;
  email: string;
  age?: number;
}

export class UpdateUserDto {
  name?: string;
  email?: string;
  age?: number;
}

export class JwtAuthGuard {}

/**
 * User management controller
 * Handles CRUD operations for users
 */
@Controller('users')
@UseGuards(JwtAuthGuard)
export class UsersController {
  /**
   * Get all users with optional filtering
   */
  @Get()
  findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string
  ) {
    return { users: [], total: 0, page, limit };
  }

  /**
   * Get a specific user by ID
   */
  @Get(':id')
  findOne(@Param('id') id: string) {
    return { id, name: '<PERSON>', email: '<EMAIL>' };
  }

  /**
   * Create a new user
   */
  @Post()
  create(@Body() createUserDto: CreateUserDto) {
    return { id: '123', ...createUserDto };
  }

  /**
   * Update an existing user
   */
  @Put(':id')
  update(
    @Param('id') id: string,
    @Body() updateUserDto: UpdateUserDto
  ) {
    return { id, ...updateUserDto };
  }

  /**
   * Delete a user
   */
  @Delete(':id')
  remove(@Param('id') id: string) {
    return { message: `User ${id} deleted successfully` };
  }
}
