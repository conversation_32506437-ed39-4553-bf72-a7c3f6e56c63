{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["ES2020"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/cli/*": ["src/cli/*"], "@/parser/*": ["src/parser/*"], "@/postman/*": ["src/postman/*"], "@/state/*": ["src/state/*"], "@/config/*": ["src/config/*"], "@/utils/*": ["src/utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*.test.ts", "**/*.spec.ts"]}