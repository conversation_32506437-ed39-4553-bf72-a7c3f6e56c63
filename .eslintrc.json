{"root": true, "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended", "airbnb-base"], "env": {"node": true, "es6": true, "jest": true}, "rules": {"import/extensions": ["error", "ignorePackages", {"js": "never", "ts": "never"}], "import/no-extraneous-dependencies": ["error", {"devDependencies": ["**/*.test.ts", "**/*.spec.ts", "**/test/**", "**/tests/**"]}], "import/prefer-default-export": "off", "class-methods-use-this": "off", "no-console": "warn", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/explicit-function-return-type": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-non-null-assertion": "error", "max-len": ["error", {"code": 120, "ignoreComments": true, "ignoreUrls": true, "ignoreStrings": true}]}, "settings": {"import/resolver": {"typescript": {"alwaysTryTypes": true, "project": "./tsconfig.json"}}}}