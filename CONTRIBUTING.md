# Contributing to nest-postman-sync

Thank you for your interest in contributing to nest-postman-sync! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js >= 16.0.0
- npm or yarn
- Git

### Development Setup

1. Fork and clone the repository:
   ```bash
   git clone https://github.com/your-username/nest-postman-sync.git
   cd nest-postman-sync
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the project:
   ```bash
   npm run build
   ```

4. Run tests to ensure everything works:
   ```bash
   npm test
   ```

## 🎯 Development Guidelines

### Code Style

We use ESL<PERSON> and <PERSON><PERSON><PERSON> to enforce consistent code style:

```bash
npm run lint        # Check for linting errors
npm run lint:fix    # Auto-fix linting errors
npm run format      # Format code with Prettier
```

### Commit Messages

We follow conventional commit format:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (no functional changes)
- `refactor:` - Code refactoring
- `test:` - Test additions or modifications
- `chore:` - Build process or auxiliary tool changes

Example: `feat: add support for OpenAPI integration`

### Testing

- Write tests for new features and bug fixes
- Ensure all tests pass before submitting a PR
- Aim for good test coverage

```bash
npm test              # Run tests in watch mode
npm run test:ci       # Run tests with coverage
npm run coverage      # Generate coverage report
```

### TypeScript

- Use strict TypeScript settings
- Provide type definitions for all functions
- Avoid `any` types where possible
- Use proper interfaces and types

## 📝 Pull Request Process

1. **Create a feature branch:**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes:**
   - Follow the coding guidelines
   - Add tests for new functionality
   - Update documentation if needed

3. **Test your changes:**
   ```bash
   npm run lint
   npm run test:ci
   npm run build
   ```

4. **Commit your changes:**
   ```bash
   git add .
   git commit -m "feat: your descriptive commit message"
   ```

5. **Push to your fork:**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a Pull Request:**
   - Use a clear, descriptive title
   - Provide detailed description of changes
   - Reference any related issues
   - Ensure CI checks pass

### PR Requirements

- [ ] Code follows style guidelines
- [ ] Tests pass and coverage is maintained
- [ ] Documentation is updated
- [ ] Commit messages follow convention
- [ ] No merge conflicts
- [ ] Description clearly explains the changes

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Clear description** of the issue
2. **Steps to reproduce** the bug
3. **Expected vs actual behavior**
4. **Environment information:**
   - Node.js version
   - Operating system
   - Package version
5. **Code samples** or test cases if applicable

Use our bug report template when creating issues.

## 💡 Feature Requests

For feature requests, please provide:

1. **Clear description** of the feature
2. **Use case** and motivation
3. **Proposed implementation** (if you have ideas)
4. **Alternatives considered**

## 🏗️ Architecture Overview

### Project Structure

```
src/
├── cli/           # CLI command implementations
├── config/        # Configuration loading and validation
├── parser/        # NestJS AST parsing
├── postman/       # Postman API integration
├── state/         # State management
├── utils/         # Utility functions (logger, etc.)
└── index.ts       # Main CLI entry point
```

### Key Components

- **CLI Commands** (`src/cli/`): Individual command handlers
- **Config Loader** (`src/config/`): Configuration management
- **AST Parser** (`src/parser/`): NestJS code analysis
- **Postman Integration** (`src/postman/`): API communication
- **State Manager** (`src/state/`): Change tracking
- **Logger** (`src/utils/`): Logging infrastructure

## 🔧 Available Scripts

- `npm run build` - Build the TypeScript project
- `npm run dev` - Run in development mode with auto-restart
- `npm test` - Run tests in watch mode
- `npm run test:ci` - Run tests with coverage
- `npm run lint` - Check for linting errors
- `npm run lint:fix` - Fix linting errors automatically
- `npm run format` - Format code with Prettier
- `npm run release` - Create a new release

## 📚 Resources

- [TypeScript Documentation](https://www.typescriptlang.org/docs/)
- [Commander.js Documentation](https://github.com/tj/commander.js/)
- [Postman API Documentation](https://learning.postman.com/docs/developer/intro-api/)
- [NestJS Documentation](https://docs.nestjs.com/)

## 🤝 Community

- Be respectful and inclusive
- Help others learn and grow
- Provide constructive feedback
- Follow our [Code of Conduct](CODE_OF_CONDUCT.md)

## ❓ Questions

If you have questions:

1. Check existing [issues](https://github.com/your-org/nest-postman-sync/issues)
2. Search [discussions](https://github.com/your-org/nest-postman-sync/discussions)
3. Create a new discussion or issue

Thank you for contributing! 🎉
