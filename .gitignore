# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist/
build/
*.tsbuildinfo

# Coverage reports
coverage/
*.lcov

# Environment variables
.env
.env.local
.env.production
.env.test

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# State files (keep example)
state.json
state-*.json

# Generated documentation
docs/generated/

# Temporary files
tmp/
temp/
.tmp/

# Postman collections (generated)
postman-collection.json
*.postman_collection.json
