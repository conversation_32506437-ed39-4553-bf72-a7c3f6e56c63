import { ClassDeclaration, PropertyDeclaration, TypeNode } from 'ts-morph';
import { logger } from '@/utils/logger';

export interface DTOExample {
  [key: string]: any;
}

export interface DTOProperty {
  name: string;
  type: string;
  isOptional: boolean;
  isArray: boolean;
  description?: string;
  example?: any;
  validation?: string[];
}

/**
 * DTO example generator with caching
 */
export class DTOGenerator {
  private cache = new Map<string, DTOExample>();
  private processingStack = new Set<string>();

  /**
   * Generate example data from DTO class
   */
  generateExample(dtoClass: ClassDeclaration): DTOExample {
    const className = dtoClass.getName();
    if (!className) {
      logger.warn('DTO class has no name, skipping', { module: 'dto-generator' });
      return {};
    }

    // Check cache first
    if (this.cache.has(className)) {
      return this.cache.get(className)!;
    }

    // Prevent circular references
    if (this.processingStack.has(className)) {
      logger.warn(`Circular reference detected for DTO: ${className}`, { module: 'dto-generator' });
      return {};
    }

    this.processingStack.add(className);

    try {
      const example = this.generateExampleFromClass(dtoClass);
      this.cache.set(className, example);
      return example;
    } finally {
      this.processingStack.delete(className);
    }
  }

  /**
   * Extract DTO properties with metadata
   */
  extractProperties(dtoClass: ClassDeclaration): DTOProperty[] {
    const properties: DTOProperty[] = [];

    for (const property of dtoClass.getProperties()) {
      const prop = this.extractPropertyInfo(property);
      if (prop) {
        properties.push(prop);
      }
    }

    return properties;
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    this.cache.clear();
    this.processingStack.clear();
  }

  private generateExampleFromClass(dtoClass: ClassDeclaration): DTOExample {
    const example: DTOExample = {};

    for (const property of dtoClass.getProperties()) {
      const propInfo = this.extractPropertyInfo(property);
      if (propInfo && !propInfo.isOptional) {
        example[propInfo.name] = this.generatePropertyExample(propInfo);
      }
    }

    return example;
  }

  private extractPropertyInfo(property: PropertyDeclaration): DTOProperty | null {
    const name = property.getName();
    if (!name) return null;

    const typeNode = property.getTypeNode();
    const isOptional = property.hasQuestionToken();
    const decorators = property.getDecorators();

    // Extract validation decorators
    const validation: string[] = [];
    for (const decorator of decorators) {
      const decoratorName = decorator.getName();
      if (decoratorName?.startsWith('Is') || decoratorName?.startsWith('Min') || decoratorName?.startsWith('Max')) {
        validation.push(decoratorName);
      }
    }

    const { type, isArray } = this.extractTypeInfo(typeNode);

    return {
      name,
      type,
      isOptional,
      isArray,
      validation,
      example: this.generatePropertyExample({ name, type, isOptional, isArray, validation }),
    };
  }

  private extractTypeInfo(typeNode: TypeNode | undefined): { type: string; isArray: boolean } {
    if (!typeNode) {
      return { type: 'any', isArray: false };
    }

    const typeText = typeNode.getText();

    // Check for array types
    if (typeText.endsWith('[]') || typeText.startsWith('Array<')) {
      const elementType = typeText.endsWith('[]') 
        ? typeText.slice(0, -2)
        : typeText.slice(6, -1);
      return { type: elementType, isArray: true };
    }

    // Handle union types (take first type)
    if (typeText.includes('|')) {
      const firstType = typeText.split('|')[0]?.trim() || 'any';
      return { type: firstType, isArray: false };
    }

    return { type: typeText, isArray: false };
  }

  private generatePropertyExample(prop: DTOProperty): any {
    if (prop.isArray) {
      const elementExample = this.generateValueByType(prop.type, prop.name);
      return [elementExample];
    }

    return this.generateValueByType(prop.type, prop.name);
  }

  private generateValueByType(type: string, propertyName: string): any {
    // Handle primitive types
    switch (type.toLowerCase()) {
      case 'string':
        return this.generateStringExample(propertyName);
      case 'number':
        return this.generateNumberExample(propertyName);
      case 'boolean':
        return true;
      case 'date':
        return new Date().toISOString();
      case 'any':
        return null;
      default:
        // For custom types/DTOs, return a placeholder object
        if (type.charAt(0).toUpperCase() === type.charAt(0)) {
          return this.generateObjectExample(type);
        }
        return this.generateStringExample(propertyName);
    }
  }

  private generateStringExample(propertyName: string): string {
    const name = propertyName.toLowerCase();

    // Generate contextual examples based on property name
    if (name.includes('email')) return '<EMAIL>';
    if (name.includes('name')) return 'John Doe';
    if (name.includes('title')) return 'Sample Title';
    if (name.includes('description')) return 'Sample description';
    if (name.includes('url') || name.includes('link')) return 'https://example.com';
    if (name.includes('phone')) return '+1234567890';
    if (name.includes('address')) return '123 Main St, City, State 12345';
    if (name.includes('id')) return 'sample-id-123';
    if (name.includes('code')) return 'ABC123';
    if (name.includes('status')) return 'active';
    if (name.includes('type')) return 'default';
    if (name.includes('category')) return 'general';
    if (name.includes('tag')) return 'sample-tag';
    if (name.includes('password')) return 'password123';
    if (name.includes('token')) return 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';

    return `sample-${name}`;
  }

  private generateNumberExample(propertyName: string): number {
    const name = propertyName.toLowerCase();

    if (name.includes('age')) return 25;
    if (name.includes('count') || name.includes('total')) return 10;
    if (name.includes('price') || name.includes('amount')) return 99.99;
    if (name.includes('id')) return 123;
    if (name.includes('year')) return new Date().getFullYear();
    if (name.includes('month')) return new Date().getMonth() + 1;
    if (name.includes('day')) return new Date().getDate();
    if (name.includes('hour')) return 12;
    if (name.includes('minute')) return 30;
    if (name.includes('second')) return 45;
    if (name.includes('percent') || name.includes('rate')) return 75;
    if (name.includes('score')) return 85;
    if (name.includes('weight')) return 70.5;
    if (name.includes('height')) return 175;
    if (name.includes('length')) return 100;
    if (name.includes('width')) return 50;

    return 42;
  }

  private generateObjectExample(typeName: string): any {
    // For unknown object types, return a simple placeholder
    return {
      id: 1,
      name: `sample-${typeName.toLowerCase()}`,
      createdAt: new Date().toISOString(),
    };
  }
}

/**
 * Global DTO generator instance
 */
export const dtoGenerator = new DTOGenerator();
