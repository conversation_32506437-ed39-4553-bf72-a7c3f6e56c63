#!/usr/bin/env node

// Configure module aliases for compiled JavaScript
import * as moduleAlias from 'module-alias';
import * as path from 'path';

// Register aliases
moduleAlias.addAlias('@', path.join(__dirname));

import { Command } from 'commander';
import { logger, configureLogger } from '@/utils/logger';
import { initCommand } from '@/cli/init';
import { generateCommand } from '@/cli/generate';
import { syncCommand } from '@/cli/sync';
import { watchCommand } from '@/cli/watch';

const program = new Command();

// Configure CLI metadata
program
  .name('nest-postman-sync')
  .description('A production-grade CLI tool for syncing NestJS projects with Postman collections')
  .version('1.0.0', '-V, --version', 'display version number');

// Global options
program
  .option('-v, --verbose', 'enable verbose logging (includes debug output)')
  .option('-q, --quiet', 'enable quiet mode (only warnings, errors, and fatal messages)')
  .option('--debug', 'enable debug mode (full stack traces and state dumps)')
  .option('--log-file <path>', 'write logs to specified file')
  .option('-c, --config <path>', 'specify configuration file path');

// Global options handler
program.hook('preAction', (thisCommand) => {
  const options = thisCommand.opts();
  
  // Configure logger based on global flags
  configureLogger({
    quiet: options['quiet'],
    verbose: options['verbose'],
    debug: options['debug'],
    logFile: options['logFile'],
  });

  logger.debug('CLI started', { 
    module: 'main',
    context: {
      command: thisCommand.name(),
      args: thisCommand.args,
      options
    }
  });
});

// Register commands
program.addCommand(initCommand);
program.addCommand(generateCommand);
program.addCommand(syncCommand);
program.addCommand(watchCommand);

// Help customization
program.addHelpText('after', `
Examples:
  $ nest-postman-sync init                    # Initialize project configuration
  $ nest-postman-sync generate                # Generate Postman collection from NestJS controllers
  $ nest-postman-sync sync                    # Sync collection to Postman workspace
  $ nest-postman-sync watch                   # Watch for changes and auto-sync
  
  $ nest-postman-sync generate --verbose      # Generate with verbose logging
  $ nest-postman-sync sync --debug            # Sync with debug information
  $ nest-postman-sync watch --config my.json # Watch with custom config file

Environment Variables:
  POSTMAN_API_KEY         Your Postman API key (required)
  POSTMAN_COLLECTION_ID   Target collection ID for syncing
  POSTMAN_WORKSPACE_ID    Target workspace ID
  NESTJS_PROJECT_ROOT     NestJS project root directory
  NESTJS_SRC_DIR          Source directory (default: src)

For more information, visit: https://github.com/your-org/nest-postman-sync
`);

// Error handling for unknown commands
program.on('command:*', (operands) => {
  logger.error(`Unknown command: ${operands[0]}`, undefined, { module: 'main' });
  logger.info('Run "nest-postman-sync --help" to see available commands', { module: 'main' });
  process.exit(1);
});

// Parse CLI arguments
async function main() {
  try {
    await program.parseAsync(process.argv);
  } catch (error) {
    logger.fatal('CLI execution failed', error as Error, { module: 'main' });
  }
}

// Handle case where no command is provided
if (!process.argv.slice(2).length) {
  program.outputHelp();
  process.exit(0);
}

// Start the CLI
main().catch((error) => {
  logger.fatal('Unexpected error during CLI execution', error, { module: 'main' });
});

export { program };
