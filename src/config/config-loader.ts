import * as fs from 'fs-extra';
import * as path from 'path';
import * as Jo<PERSON> from 'joi';
import { config } from 'dotenv';
import { logger } from '@/utils/logger';

export interface PostmanConfig {
  postman: {
    apiKey: string;
    collectionId?: string;
    workspaceId?: string;
    baseUrl?: string;
  };
  nestjs: {
    projectRoot: string;
    autoDiscovery: boolean;
    srcDirs: string | string[];
    controllersPattern: string[];
    excludePatterns: string[];
    globalPrefix?: string;
    autoDetectGlobalPrefix?: boolean;
    isMicroservice?: boolean;
    microservicePrefixes?: Record<string, string>;
  };
  sync: {
    outputFile?: string;
    watchMode?: boolean;
    watchPaths?: string[];
    debounceMs?: number;
  };
  options: {
    generateDocs?: boolean;
    includePrivate?: boolean;
    includeDeprecated?: boolean;
    overwriteExisting?: boolean;
  };
}

const configSchema = Joi.object({
  postman: Joi.object({
    apiKey: Joi.string().required(),
    collectionId: Joi.string().optional(),
    workspaceId: Joi.string().optional(),
    baseUrl: Joi.string().uri().default('https://api.postman.com'),
  }).required(),
  nestjs: Joi.object({
    projectRoot: Joi.string().default('.'),
    // Auto-discovery mode (default)
    autoDiscovery: Joi.boolean().default(true),
    // Manual configuration (when autoDiscovery is false)
    srcDirs: Joi.alternatives().try(
      Joi.string(),
      Joi.array().items(Joi.string())
    ).when('autoDiscovery', {
      is: false,
      then: Joi.required(),
      otherwise: Joi.optional().default(['src'])
    }),
    controllersPattern: Joi.array().items(Joi.string()).default(['**/*.controller.ts']),
    excludePatterns: Joi.array().items(Joi.string()).default([
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/.git/**',
      '**/*.spec.ts',
      '**/*.test.ts',
      '**/coverage/**',
      '**/tmp/**',
      '**/temp/**'
    ]),
    globalPrefix: Joi.string().optional().allow('').description('Global API prefix (e.g., "/api", "/api/v1")'),
    autoDetectGlobalPrefix: Joi.boolean().default(true).description('Whether to auto-detect global prefix'),
    isMicroservice: Joi.boolean().default(false).description('Whether this is a microservice application'),
    microservicePrefixes: Joi.object().pattern(Joi.string(), Joi.string().allow('')).optional().description('Microservice-specific prefixes'),
  }).required(),
  sync: Joi.object({
    outputFile: Joi.string().optional(),
    watchMode: Joi.boolean().default(false),
    watchPaths: Joi.array().items(Joi.string()).default(['src/**/*.ts']),
    debounceMs: Joi.number().integer().min(100).default(1000),
  }).default({}),
  options: Joi.object({
    generateDocs: Joi.boolean().default(true),
    includePrivate: Joi.boolean().default(false),
    includeDeprecated: Joi.boolean().default(false),
    overwriteExisting: Joi.boolean().default(false),
  }).default({}),
});

export class ConfigLoader {
  private static instance: ConfigLoader;
  private config: PostmanConfig | null = null;
  private configPath: string | null = null;

  private constructor() {}

  static getInstance(): ConfigLoader {
    if (!ConfigLoader.instance) {
      ConfigLoader.instance = new ConfigLoader();
    }
    return ConfigLoader.instance;
  }

  /**
   * Load configuration from file and environment
   */
  async load(configFilePath?: string): Promise<PostmanConfig> {
    try {
      console.log('DEBUG - Starting config load, input path:', configFilePath);

      // Load environment variables
      this.loadEnvironment();

      // Determine config file path
      const resolvedConfigPath = this.resolveConfigPath(configFilePath);
      this.configPath = resolvedConfigPath;

      console.log('DEBUG - Resolved config path:', resolvedConfigPath);
      console.log('DEBUG - File exists:', require('fs').existsSync(resolvedConfigPath));

      logger.debug(`Loading configuration from: ${resolvedConfigPath}`, { module: 'config' });

      // Load and parse config file
      const rawConfig = await this.loadConfigFile(resolvedConfigPath);
      console.log('DEBUG - Config file path:', resolvedConfigPath);
      console.log('DEBUG - Raw config from file:', JSON.stringify(rawConfig, null, 2));

      // Merge with environment variables
      const mergedConfig = this.mergeWithEnvironment(rawConfig);

      // Debug logging
      logger.debug('Raw config loaded:', { module: 'config' });
      logger.debug('Merged config:', { module: 'config' });
      console.log('DEBUG - Raw config:', JSON.stringify(rawConfig, null, 2));
      console.log('DEBUG - Merged config:', JSON.stringify(mergedConfig, null, 2));

      // Validate configuration
      const { error, value } = configSchema.validate(mergedConfig, {
        allowUnknown: false,
        stripUnknown: true,
      });

      if (error) {
        throw new Error(`Configuration validation failed: ${error.message}`);
      }

      this.config = value as PostmanConfig;
      logger.info('Configuration loaded successfully', { module: 'config' });
      
      return this.config;
    } catch (error) {
      logger.error('Failed to load configuration', error as Error, { module: 'config' });
      throw error;
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): PostmanConfig {
    if (!this.config) {
      throw new Error('Configuration not loaded. Call load() first.');
    }
    return this.config;
  }

  /**
   * Reload configuration
   */
  async reload(): Promise<PostmanConfig> {
    this.config = null;
    return this.load(this.configPath || undefined);
  }

  /**
   * Load environment variables
   */
  private loadEnvironment(): void {
    // Look for .env file in current directory or project root
    const envPaths = [
      path.join(process.cwd(), '.env'),
      path.join(process.cwd(), '.env.local'),
    ];

    for (const envPath of envPaths) {
      if (fs.existsSync(envPath)) {
        config({ path: envPath });
        logger.debug(`Loaded environment from: ${envPath}`, { module: 'config' });
        break;
      }
    }
  }

  /**
   * Resolve configuration file path
   */
  private resolveConfigPath(configFilePath?: string): string {
    if (configFilePath) {
      return path.resolve(configFilePath);
    }

    // Look for config file in standard locations
    const candidates = [
      'postman.config.json',
      'postman.config.js',
      '.postmanrc.json',
      'package.json',
    ];

    for (const candidate of candidates) {
      const fullPath = path.join(process.cwd(), candidate);
      if (fs.existsSync(fullPath)) {
        if (candidate === 'package.json') {
          // Check if package.json has postman config section
          const packageJson = fs.readJsonSync(fullPath);
          if (packageJson.postmanSync) {
            return fullPath;
          }
        } else {
          return fullPath;
        }
      }
    }

    throw new Error('No configuration file found. Please create postman.config.json or run nest-postman-sync init');
  }

  /**
   * Load configuration file
   */
  private async loadConfigFile(configPath: string): Promise<any> {
    try {
      if (configPath.endsWith('package.json')) {
        const packageJson = await fs.readJson(configPath);
        return packageJson.postmanSync || {};
      }

      if (configPath.endsWith('.js')) {
        // Dynamic import for JS config files
        delete require.cache[require.resolve(configPath)];
        const config = require(configPath);
        return config.default || config;
      }

      // JSON config file
      return await fs.readJson(configPath);
    } catch (error) {
      throw new Error(`Failed to load config file ${configPath}: ${(error as Error).message}`);
    }
  }

  /**
   * Merge configuration with environment variables
   */
  private mergeWithEnvironment(config: any): any {
    const envOverrides: any = {};

    // Map environment variables to config structure
    if (process.env['POSTMAN_API_KEY']) {
      envOverrides.postman = { ...envOverrides.postman, apiKey: process.env['POSTMAN_API_KEY'] };
    }

    if (process.env['POSTMAN_COLLECTION_ID']) {
      envOverrides.postman = { ...envOverrides.postman, collectionId: process.env['POSTMAN_COLLECTION_ID'] };
    }

    if (process.env['POSTMAN_WORKSPACE_ID']) {
      envOverrides.postman = { ...envOverrides.postman, workspaceId: process.env['POSTMAN_WORKSPACE_ID'] };
    }

    if (process.env['NESTJS_PROJECT_ROOT']) {
      envOverrides.nestjs = { ...envOverrides.nestjs, projectRoot: process.env['NESTJS_PROJECT_ROOT'] };
    }

    if (process.env['NESTJS_SRC_DIR']) {
      envOverrides.nestjs = { ...envOverrides.nestjs, srcDir: process.env['NESTJS_SRC_DIR'] };
    }

    // Deep merge config with environment overrides
    return this.deepMerge(config, envOverrides);
  }

  /**
   * Deep merge objects
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };

    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
          result[key] = this.deepMerge(result[key] || {}, source[key]);
        } else {
          result[key] = source[key];
        }
      }
    }

    return result;
  }

  /**
   * Validate required environment variables are present
   */
  static validateEnvironment(): void {
    const required = ['POSTMAN_API_KEY'];
    const missing = required.filter(key => !process.env[key]);

    if (missing.length > 0) {
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
  }

  /**
   * Create default configuration file
   */
  static async createDefaultConfig(outputPath = 'postman.config.json'): Promise<void> {
    const defaultConfig = {
      postman: {
        apiKey: '${POSTMAN_API_KEY}',
        collectionId: '${POSTMAN_COLLECTION_ID}',
        workspaceId: '${POSTMAN_WORKSPACE_ID}',
        baseUrl: 'https://api.postman.com'
      },
      nestjs: {
        projectRoot: '.',
        autoDiscovery: true,
        srcDirs: ['src'], // Used only when autoDiscovery is false
        controllersPattern: ['**/*.controller.ts'],
        excludePatterns: [
          '**/node_modules/**',
          '**/dist/**',
          '**/build/**',
          '**/.git/**',
          '**/*.spec.ts',
          '**/*.test.ts',
          '**/coverage/**',
          '**/tmp/**',
          '**/temp/**'
        ],
        globalPrefix: '', // User-provided global prefix
        autoDetectGlobalPrefix: true, // Auto-detect global prefix by default
        isMicroservice: false,
        microservicePrefixes: {} // Microservice-specific prefixes
      },
      sync: {
        outputFile: 'postman-collection.json',
        watchMode: false,
        watchPaths: ['src/**/*.ts'],
        debounceMs: 1000
      },
      options: {
        generateDocs: true,
        includePrivate: false,
        includeDeprecated: false,
        overwriteExisting: true
      }
    };

    await fs.writeJson(outputPath, defaultConfig, { spaces: 2 });
    logger.info(`Created default configuration at: ${outputPath}`, { module: 'config' });
  }
}

// Export singleton instance
export const configLoader = ConfigLoader.getInstance();

// Convenience function for loading config
export async function loadConfig(configFilePath?: string): Promise<PostmanConfig> {
  return configLoader.load(configFilePath);
}
