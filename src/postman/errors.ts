/**
 * Custom error class for Postman API failures
 */
export class PostmanError extends Error {
  constructor(
    public meta: {
      method: string;
      url: string;
      status?: number;
      body?: any;
    },
    message: string
  ) {
    super(message);
    this.name = 'PostmanError';
    
    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, PostmanError);
    }
  }

  /**
   * Get a formatted error message for logging
   */
  getFormattedMessage(): string {
    const { method, url, status } = this.meta;
    const statusText = status ? ` · ${status}` : '';
    return `${method} ${url}${statusText} – ${this.message}`;
  }

  /**
   * Get error details for debugging
   */
  getDetails(): Record<string, any> {
    return {
      method: this.meta.method,
      url: this.meta.url,
      status: this.meta.status,
      body: this.meta.body,
      message: this.message,
      stack: this.stack,
    };
  }
}
