import { Command } from 'commander';
import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import { logger } from '@/utils/logger';
import { loadConfig } from '@/config/config-loader';
import { StateManager } from '@/state/state-manager';

export const syncCommand = new Command('sync')
  .description('Sync generated collection to Postman workspace')
  .option('-i, --input <file>', 'Input collection file', 'postman-collection.json')
  .option('--collection-id <id>', 'Specific collection ID to update')
  .option('--dry-run', 'Show what would be synced without making changes', false)
  .action(async (options) => {
    try {
      logger.info('Starting collection sync', { module: 'sync' });

      // Load configuration
      const config = await loadConfig();
      logger.debug('Configuration loaded', { module: 'sync' });

      // Check if input file exists
      const inputPath = path.resolve(options.input);
      if (!(await fs.pathExists(inputPath))) {
        console.error(`❌ Collection file not found: ${inputPath}`);
        console.log('💡 Run "nest-postman-sync generate" first to create the collection.');
        process.exit(1);
      }

      // Load collection
      const collection = await fs.readJson(inputPath);
      logger.debug('Collection loaded from file', { module: 'sync' });

      // Get Postman API configuration
      const apiKey = config.postman.apiKey;
      const workspaceId = config.postman.workspaceId;
      const collectionId = options.collectionId || config.postman.collectionId;

      if (!apiKey) {
        console.error('❌ Postman API key not configured');
        console.log('💡 Set POSTMAN_API_KEY environment variable or run "nest-postman-sync init"');
        process.exit(1);
      }

      if (!collectionId) {
        console.error('❌ Collection ID not configured');
        console.log('💡 Run "nest-postman-sync init" to set up collection configuration');
        process.exit(1);
      }

      // Prepare API client
      const postmanApi = axios.create({
        baseURL: config.postman.baseUrl || 'https://api.postman.com',
        headers: {
          'X-API-Key': apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (options.dryRun) {
        console.log('🔍 Dry run mode - showing what would be synced:');
        console.log(`📁 Workspace ID: ${workspaceId || 'default'}`);
        console.log(`📄 Collection ID: ${collectionId}`);
        console.log(`📊 Collection: ${collection.info.name}`);
        console.log(`🔗 Endpoints: ${countEndpoints(collection)}`);
        console.log('✅ Dry run completed - no changes made');
        return;
      }

      // Update collection
      console.log('🔄 Syncing collection to Postman...');

      try {
        const response = await postmanApi.put(`/collections/${collectionId}`, {
          collection,
        });

        if (response.status === 200) {
          console.log('✅ Collection synced successfully');

          // Update state
          const stateManager = new StateManager();
          await stateManager.load();

          stateManager.updateCollectionState({
            id: collectionId,
            name: collection.info.name,
            lastSync: new Date().toISOString(),
            endpointCount: countEndpoints(collection),
          });

          stateManager.updateLastSync();
          await stateManager.save();

          logger.info('Collection sync completed successfully', {
            module: 'sync',
            context: {
              collectionId,
              endpointCount: countEndpoints(collection),
            }
          });
        } else {
          throw new Error(`Unexpected response status: ${response.status}`);
        }
      } catch (apiError: any) {
        if (apiError.response) {
          const status = apiError.response.status;
          const message = apiError.response.data?.error?.message || 'Unknown API error';

          console.error(`❌ Postman API error (${status}): ${message}`);

          if (status === 401) {
            console.log('💡 Check your Postman API key');
          } else if (status === 404) {
            console.log('💡 Collection not found - check your collection ID');
          }
        } else {
          console.error('❌ Network error:', apiError.message);
        }

        logger.error('Collection sync failed', apiError, { module: 'sync' });
        process.exit(1);
      }

    } catch (error) {
      logger.error('Sync command failed', error as Error, { module: 'sync' });
      console.error('❌ Sync failed:', (error as Error).message);
      process.exit(1);
    }
  });

/**
 * Count total endpoints in a collection
 */
function countEndpoints(collection: any): number {
  let count = 0;

  function countItems(items: any[]): void {
    for (const item of items) {
      if (item.request) {
        count++;
      } else if (item.item) {
        countItems(item.item);
      }
    }
  }

  if (collection.item) {
    countItems(collection.item);
  }

  return count;
}
