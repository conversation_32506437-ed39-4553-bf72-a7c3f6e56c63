import { Command } from 'commander';
import inquirer from 'inquirer';
import * as fs from 'fs-extra';
import * as path from 'path';
import { config as dotenvConfig } from 'dotenv';
import { logger } from '@/utils/logger';
import { postmanClient, Workspace, Collection } from '@/postman/client';
import { PostmanError } from '@/postman/errors';
import { GlobalPrefixDetector } from '@/utils/prefix-detector';

// Load environment variables from .env file in current directory
dotenvConfig({ path: path.join(process.cwd(), '.env') });

/**
 * Configuration interfaces
 */

interface PostmanSyncConfig {
  postman: {
    apiKey: string;
    collectionId: string;
    workspaceId: string;
    baseUrl: string;
  };
  nestjs: {
    projectRoot: string;
    autoDiscovery: boolean;
    srcDirs: string[];
    controllersPattern: string[];
    excludePatterns: string[];
    globalPrefix?: string;
    autoDetectGlobalPrefix: boolean;
    isMicroservice?: boolean;
    microservicePrefixes?: Record<string, string>;
  };
  sync: {
    outputFile: string;
    watchMode: boolean;
    watchPaths: string[];
    debounceMs: number;
  };
  options: {
    generateDocs: boolean;
    includePrivate: boolean;
    includeDeprecated: boolean;
    overwriteExisting: boolean;
  };
}

/**
 * Write environment variable to .env file
 */
async function writeEnvVariable(key: string, value: string): Promise<void> {
  const envPath = path.join(process.cwd(), '.env');
  let envContent = '';
  
  // Read existing .env file if it exists
  if (await fs.pathExists(envPath)) {
    envContent = await fs.readFile(envPath, 'utf-8');
  }
  
  // Check if the key already exists
  const keyRegex = new RegExp(`^${key}=.*$`, 'm');
  const newLine = `${key}=${value}`;
  
  if (keyRegex.test(envContent)) {
    // Replace existing key
    envContent = envContent.replace(keyRegex, newLine);
  } else {
    // Add new key
    if (envContent && !envContent.endsWith('\n')) {
      envContent += '\n';
    }
    envContent += newLine + '\n';
  }
  
  await fs.writeFile(envPath, envContent);
  logger.debug(`Updated .env file with ${key}`, { module: 'init' });
}

/**
 * Prompt for Postman API key
 */
async function promptForApiKey(): Promise<string> {
  const { apiKeyChoice } = await inquirer.prompt([
    {
      type: 'list',
      name: 'apiKeyChoice',
      message: 'How would you like to provide your Postman API Key?',
      choices: [
        { name: 'Enter API key now', value: 'enter' },
        { name: 'Use existing value from .env file', value: 'env' },
      ],
    },
  ]);

  if (apiKeyChoice === 'env') {
    const apiKey = process.env['POSTMAN_API_KEY'];
    if (!apiKey) {
      console.error('❌  POSTMAN_API_KEY not found in environment variables.');
      console.log('💡  Please set POSTMAN_API_KEY in your .env file or choose to enter it now.');
      process.exit(1);
    }
    return apiKey;
  }

  const { apiKey } = await inquirer.prompt([
    {
      type: 'password',
      name: 'apiKey',
      message: 'Enter your Postman API Key:',
      mask: '*',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'API key cannot be empty';
        }
        return true;
      },
    },
  ]);

  // Write to .env file
  await writeEnvVariable('POSTMAN_API_KEY', apiKey);
  
  // Set in current process for immediate use
  process.env['POSTMAN_API_KEY'] = apiKey;
  
  console.log('✅  API key saved to .env file');
  return apiKey;
}

/**
 * Prompt for workspace selection
 */
async function promptForWorkspace(): Promise<{ workspace: Workspace, isNew: boolean }> {
  console.log('📂  Fetching your Postman workspaces...');
  try {
    const workspaces = await postmanClient.listWorkspaces();
    if (workspaces.length === 0) {
      console.log('ℹ️   No workspaces found. Creating a new one.');
      return await promptCreateWorkspace();
    }
    const choices = [
      ...workspaces.map(ws => ({
        name: `${ws.name} (${ws.type}) - ${ws.id}`,
        value: ws,
      })),
      {
        name: '➕ Create a new workspace',
        value: 'create_new',
      },
    ];
    const { selectedWorkspace } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedWorkspace',
        message: 'Select a workspace to use:',
        choices,
        pageSize: 10,
      },
    ]);
    if (selectedWorkspace === 'create_new') {
      return await promptCreateWorkspace();
    }
    return { workspace: selectedWorkspace as Workspace, isNew: false };
  } catch (error) {
    if (error instanceof PostmanError) {
      const status = error.meta?.status || '';
      logger.error(`Failed to fetch workspaces: ${error.getFormattedMessage()}`);
      console.error(`❌  [postman] POST /workspaces – ${status} : ${error.message}`);
      if (process.argv.includes('--debug')) {
        console.error(error.stack);
      }
      console.error('❌  Failed to list workspaces. Please check your API key or network connection.');
    } else {
      logger.error(`Unexpected error: ${(error as Error).message}`);
      console.error('❌  An unexpected error occurred while fetching workspaces.');
    }
    process.exit(1);
  }
}

/**
 * Prompt to create a new workspace
 */
async function promptCreateWorkspace(): Promise<{ workspace: Workspace, isNew: boolean }> {
  const { workspaceName, workspaceType, workspaceDescription } = await inquirer.prompt([
    {
      type: 'input',
      name: 'workspaceName',
      message: 'Enter the name for the new workspace:',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'Workspace name cannot be empty';
        }
        return true;
      },
    },
    {
      type: 'list',
      name: 'workspaceType',
      message: 'Select workspace type:',
      choices: [
        { name: 'Personal', value: 'personal' },
        { name: 'Team', value: 'team' },
      ],
      default: 'personal',
    },
    {
      type: 'input',
      name: 'workspaceDescription',
      message: 'Enter a description for the workspace (optional):',
    },
  ]);
  try {
    console.log('✨  Creating new workspace...');
    const workspace = await postmanClient.createWorkspace(
      workspaceName.trim(),
      workspaceType,
      workspaceDescription?.trim() || undefined
    );
    console.log(`✅  Created workspace: ${workspace.name}`);
    return { workspace, isNew: true };
  } catch (error) {
    if (error instanceof PostmanError) {
      const status = error.meta?.status || '';
      logger.error(`❌  [postman] POST /workspaces – ${status} : ${error.message}`);
      if (process.argv.includes('--debug')) {
        console.error(error.stack);
      }
      console.error('❌  Failed to create workspace. Please try again.');
    } else {
      logger.error(`❌  [postman] POST /workspaces – : ${(error as Error).message}`);
      if (process.argv.includes('--debug')) {
        console.error((error as Error).stack);
      }
      console.error('❌  An unexpected error occurred while creating the workspace.');
    }
    process.exit(1);
  }
}

/**
 * Prompt for collection selection
 */
async function promptForCollection(workspaceId: string): Promise<Collection> {
  console.log('📄  Fetching collections in workspace...');
  
  try {
    const collections = await postmanClient.listCollections(workspaceId);
    
    if (collections.length === 0) {
      console.log('ℹ️   No collections found in this workspace. Creating a new one.');
      return await promptCreateCollection(workspaceId);
    }

    const choices = [
      ...collections.map(collection => ({
        name: `${collection.name} - ${collection.uid}`,
        value: collection,
      })),
      {
        name: '➕ Create a new collection',
        value: 'create_new',
      },
    ];

    const { selectedCollection } = await inquirer.prompt([
      {
        type: 'list',
        name: 'selectedCollection',
        message: 'Select an existing collection to sync:',
        choices,
        pageSize: 10,
      },
    ]);

    if (selectedCollection === 'create_new') {
      return await promptCreateCollection(workspaceId);
    }

    return selectedCollection as Collection;
  } catch (error) {
    if (error instanceof PostmanError) {
      const status = error.meta?.status || '';
      logger.error(`Failed to fetch collections: ${error.getFormattedMessage()}`);
      console.error(`❌  [postman] POST /collections?workspace=${workspaceId} – ${status} : ${error.message}`);
      if (process.argv.includes('--debug')) {
        console.error(error.stack);
      }
      console.error('❌  Failed to list collections. Please try again.');
    } else {
      logger.error(`Unexpected error: ${(error as Error).message}`);
      console.error('❌  An unexpected error occurred while fetching collections.');
    }
    process.exit(1);
  }
}

/**
 * Prompt to create a new collection
 */
async function promptCreateCollection(workspaceId: string): Promise<Collection> {
  const { collectionName, collectionDescription } = await inquirer.prompt([
    {
      type: 'input',
      name: 'collectionName',
      message: 'Enter the name for the new collection:',
      default: 'NestJS API Collection',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'Collection name cannot be empty';
        }
        return true;
      },
    },
    {
      type: 'input',
      name: 'collectionDescription',
      message: 'Enter a description for the collection (optional):',
      default: 'Auto-generated collection from NestJS controllers',
    },
  ]);

  try {
    console.log('✨  Creating new collection...');
    // Always use the required schema
    const collection = await postmanClient.createCollection(
      workspaceId,
      collectionName.trim(),
      collectionDescription?.trim() || undefined
    );
    console.log(`✅  Created collection: ${collection.name}`);
    return collection;
  } catch (error) {
    if (error instanceof PostmanError) {
      const status = error.meta?.status || '';
      logger.error(`❌  [postman] POST /collections?workspace=${workspaceId} – ${status} : ${error.message}`);
      if (process.argv.includes('--debug')) {
        console.error(error.stack);
      }
      console.error('❌  Failed to create collection. Please try again.');
    } else {
      logger.error(`❌  [postman] POST /collections?workspace=${workspaceId} – : ${(error as Error).message}`);
      if (process.argv.includes('--debug')) {
        console.error((error as Error).stack);
      }
      console.error('❌  An unexpected error occurred while creating the collection.');
    }
    process.exit(1);
  }
}



/**
 * Prompt for NestJS project configuration
 */
async function promptForNestJSSettings(): Promise<{
  projectRoot: string;
  autoDiscovery: boolean;
  srcDirs: string[];
  globalPrefix: string;
  isMicroservice: boolean;
  microservicePrefixes?: Record<string, string>;
  autoDetectGlobalPrefix: boolean;
}> {
  console.log('🏗️  Configuring NestJS project settings...');

  const { projectRoot, autoDiscovery } = await inquirer.prompt([
    {
      type: 'input',
      name: 'projectRoot',
      message: 'Enter your NestJS project root directory:',
      default: '.',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'Project root cannot be empty';
        }
        return true;
      },
    },
    {
      type: 'confirm',
      name: 'autoDiscovery',
      message: 'Enable auto-discovery of controllers across the entire project?',
      default: true,
    },
  ]);

  let srcDirs: string[] = ['src'];
  if (!autoDiscovery) {
    const { srcDirsInput } = await inquirer.prompt([
      {
        type: 'input',
        name: 'srcDirsInput',
        message: 'Enter source directories (comma-separated):',
        default: 'src',
        validate: (input: string) => {
          if (!input || input.trim().length === 0) {
            return 'Source directories cannot be empty';
          }
          return true;
        },
      },
    ]);
    srcDirs = srcDirsInput.split(',').map((dir: string) => dir.trim());
  }

  // Detect architecture first
  console.log('🔍  Analyzing project architecture...');
  const detector = new GlobalPrefixDetector(projectRoot.trim());
  let detectionResult;
  try {
    detectionResult = await detector.detectGlobalPrefix(projectRoot.trim());
  } finally {
    detector.dispose();
  }

  const { isMicroservice } = await inquirer.prompt([
    {
      type: 'confirm',
      name: 'isMicroservice',
      message: `Is this a microservice application? ${detectionResult.architecture !== 'monolithic' ? '(Auto-detected: Yes)' : '(Auto-detected: No)'}`,
      default: detectionResult.architecture !== 'monolithic',
    },
  ]);

  let globalPrefix = '';
  let microservicePrefixes: Record<string, string> | undefined;
  let autoDetectGlobalPrefix = true;

  if (isMicroservice) {
    console.log('🔧  Configuring microservice prefixes...');
    console.log('💡  In microservice architecture, each service can have its own API prefix.');
    console.log('💡  For example: auth service might use "/auth", user service might use "/users", etc.');

    if (detectionResult.microservicePrefixes && Object.keys(detectionResult.microservicePrefixes).length > 0) {
      console.log('\n📋  Auto-detected some potential microservice prefixes:');
      Object.entries(detectionResult.microservicePrefixes).forEach(([service, prefix]) => {
        console.log(`   • ${service}: ${prefix || '(no prefix)'}`);
      });
      console.log('⚠️  Note: These are best-guess detections and may need adjustment.');

      const { useDetectedPrefixes } = await inquirer.prompt([
        {
          type: 'confirm',
          name: 'useDetectedPrefixes',
          message: 'Use the auto-detected prefixes as a starting point?',
          default: false,
        },
      ]);

      if (useDetectedPrefixes) {
        microservicePrefixes = { ...detectionResult.microservicePrefixes };

        const { modifyPrefixes } = await inquirer.prompt([
          {
            type: 'confirm',
            name: 'modifyPrefixes',
            message: 'Would you like to modify or add more service prefixes?',
            default: true,
          },
        ]);

        if (modifyPrefixes) {
          microservicePrefixes = await promptForMicroservicePrefixes(microservicePrefixes);
        }
        autoDetectGlobalPrefix = false;
      }
    }

    if (!microservicePrefixes) {
      const { configurationChoice } = await inquirer.prompt([
        {
          type: 'list',
          name: 'configurationChoice',
          message: 'How would you like to configure microservice prefixes?',
          choices: [
            { name: 'Configure manually now (recommended for precise control)', value: 'manual' },
            { name: 'Skip for now - I\'ll configure during generation or in config file', value: 'skip' },
          ],
          default: 'manual',
        },
      ]);

      if (configurationChoice === 'manual') {
        microservicePrefixes = await promptForMicroservicePrefixes();
        autoDetectGlobalPrefix = false;
      } else {
        console.log('ℹ️  You can configure microservice prefixes later in the postman.config.json file.');
        console.log('ℹ️  Example configuration:');
        console.log('   "microservicePrefixes": {');
        console.log('     "auth": "/auth",');
        console.log('     "users": "/users",');
        console.log('     "orders": "/api/v1/orders"');
        console.log('   }');
        autoDetectGlobalPrefix = true;
      }
    }
  } else {
    // Monolithic application - ask for global prefix
    const { globalPrefixInput } = await inquirer.prompt([
      {
        type: 'input',
        name: 'globalPrefixInput',
        message: `Enter global API prefix (e.g., "/api", "/api/v1", leave empty for none): ${detectionResult.prefix ? `(Auto-detected: "${detectionResult.prefix}")` : ''}`,
        default: detectionResult.prefix || '',
        validate: (input: string) => {
          if (input && !input.startsWith('/')) {
            return 'Global prefix should start with "/" or be empty';
          }
          return true;
        },
      },
    ]);
    globalPrefix = globalPrefixInput || '';
    autoDetectGlobalPrefix = false;
  }

  return {
    projectRoot: projectRoot.trim(),
    autoDiscovery,
    srcDirs,
    globalPrefix,
    isMicroservice,
    ...(microservicePrefixes && { microservicePrefixes }),
    autoDetectGlobalPrefix,
  };
}

/**
 * Prompt for manual microservice prefix configuration
 */
async function promptForMicroservicePrefixes(initialPrefixes?: Record<string, string>): Promise<Record<string, string>> {
  const prefixes: Record<string, string> = { ...(initialPrefixes || {}) };

  console.log('\n🔧  Configure microservice prefixes:');
  console.log('💡  Enter service names and their corresponding API prefixes.');
  console.log('💡  Service names should match how your services are organized (e.g., "auth", "users", "orders").');
  console.log('💡  Prefixes are the URL paths for each service (e.g., "/auth", "/api/v1/users").');
  console.log('💡  Leave prefix empty if the service has no prefix.');

  if (Object.keys(prefixes).length > 0) {
    console.log('\n�  Current configuration:');
    Object.entries(prefixes).forEach(([service, prefix]) => {
      console.log(`   • ${service}: ${prefix || '(no prefix)'}`);
    });

    const { modifyExisting } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'modifyExisting',
        message: 'Would you like to modify existing service prefixes?',
        default: false,
      },
    ]);

    if (modifyExisting) {
      for (const [serviceName, currentPrefix] of Object.entries(prefixes)) {
        const { action } = await inquirer.prompt([
          {
            type: 'list',
            name: 'action',
            message: `Service "${serviceName}" (current: ${currentPrefix || '(no prefix)'}):`,
            choices: [
              { name: 'Keep current prefix', value: 'keep' },
              { name: 'Modify prefix', value: 'modify' },
              { name: 'Remove service', value: 'remove' },
            ],
          },
        ]);

        if (action === 'modify') {
          const { newPrefix } = await inquirer.prompt([
            {
              type: 'input',
              name: 'newPrefix',
              message: `Enter new prefix for "${serviceName}":`,
              default: currentPrefix,
              validate: (input: string) => {
                if (input && !input.startsWith('/')) {
                  return 'Service prefix should start with "/" or be empty';
                }
                return true;
              },
            },
          ]);
          prefixes[serviceName] = newPrefix.trim() || '';
        } else if (action === 'remove') {
          delete prefixes[serviceName];
          console.log(`❌  Removed service: ${serviceName}`);
        }
      }
    }
  }

  console.log('\n➕  Add new services (press Enter with empty service name to finish):');

  while (true) {
    const { serviceName } = await inquirer.prompt([
      {
        type: 'input',
        name: 'serviceName',
        message: 'Enter service name (or press Enter to finish):',
        validate: (input: string) => {
          if (input && prefixes[input.trim()]) {
            return 'Service name already exists. Please enter a different name.';
          }
          return true;
        },
      },
    ]);

    if (!serviceName || serviceName.trim() === '') {
      break;
    }

    const { servicePrefix } = await inquirer.prompt([
      {
        type: 'input',
        name: 'servicePrefix',
        message: `Enter API prefix for "${serviceName}" (e.g., "/auth", "/api/v1/users", leave empty for no prefix):`,
        validate: (input: string) => {
          if (input && !input.startsWith('/')) {
            return 'Service prefix should start with "/" or be empty';
          }
          return true;
        },
      },
    ]);

    prefixes[serviceName.trim()] = servicePrefix.trim() || '';
    console.log(`✅  Added: ${serviceName} → ${servicePrefix || '(no prefix)'}\n`);
  }

  if (Object.keys(prefixes).length === 0) {
    console.log('⚠️  No microservice prefixes configured.');
    console.log('💡  You can add them later in the postman.config.json file.');
  } else {
    console.log('\n📋  Final microservice prefix configuration:');
    Object.entries(prefixes).forEach(([service, prefix]) => {
      console.log(`   • ${service}: ${prefix || '(no prefix)'}`);
    });
  }

  return prefixes;
}

/**
 * Prompt for sync and output settings
 */
async function promptForSyncSettings(): Promise<{
  baseUrl: string;
  outputFile: string;
  watchMode: boolean;
  generateDocs: boolean;
  includePrivate: boolean;
  includeDeprecated: boolean;
}> {
  const settings = await inquirer.prompt([
    {
      type: 'input',
      name: 'baseUrl',
      message: 'Enter your API base URL:',
      default: 'http://localhost:3000',
      validate: (input: string) => {
        if (!input || input.trim().length === 0) {
          return 'Base URL cannot be empty';
        }
        try {
          new URL(input);
          return true;
        } catch {
          return 'Please enter a valid URL';
        }
      },
    },
    {
      type: 'input',
      name: 'outputFile',
      message: 'Enter output file name:',
      default: 'postman-collection.json',
    },
    {
      type: 'confirm',
      name: 'watchMode',
      message: 'Enable watch mode by default?',
      default: false,
    },
    {
      type: 'confirm',
      name: 'generateDocs',
      message: 'Generate documentation for endpoints?',
      default: true,
    },
    {
      type: 'confirm',
      name: 'includePrivate',
      message: 'Include private methods in the collection?',
      default: false,
    },
    {
      type: 'confirm',
      name: 'includeDeprecated',
      message: 'Include deprecated methods in the collection?',
      default: false,
    },
  ]);

  return {
    baseUrl: settings.baseUrl.trim(),
    outputFile: settings.outputFile.trim(),
    watchMode: settings.watchMode,
    generateDocs: settings.generateDocs,
    includePrivate: settings.includePrivate,
    includeDeprecated: settings.includeDeprecated,
  };
}

/**
 * Write configuration file
 */
async function writeConfigFile(config: PostmanSyncConfig): Promise<void> {
  const configPath = path.join(process.cwd(), 'postman.config.json');
  
  try {
    await fs.writeJson(configPath, config, { spaces: 2 });
    logger.info(`Configuration saved to ${configPath}`, { module: 'init' });
  } catch (error) {
    logger.error('Failed to write configuration file', error as Error, { module: 'init' });
    console.error('❌  Failed to save configuration file.');
    process.exit(1);
  }
}

/**
 * Main init command implementation
 */
export const initCommand = new Command('init')
  .description('Initialize the configuration for nest-postman-sync')
  .action(async () => {
    console.log('🔧  Initializing nest-postman-sync...\n');
    logger.info('Starting init command', { module: 'init' });

    try {
      // Step 1: Get API key
      await promptForApiKey();
      console.log();

      // Step 2: Select or create workspace
      const { workspace, isNew } = await promptForWorkspace();
      console.log(`📁  Selected workspace: ${workspace.name}\n`);

      // Step 3: Select or create collection
      let collection: Collection;
      if (isNew) {
        // Always create a new collection for a new workspace
        collection = await promptCreateCollection(workspace.id);
      } else {
        // Prompt for existing or new collection in the selected workspace
        collection = await promptForCollection(workspace.id);
      }
      console.log(`📄  Selected collection: ${collection.name}\n`);

      // Step 4: Configure NestJS settings
      const nestjsSettings = await promptForNestJSSettings();
      console.log();

      // Step 5: Configure sync settings
      console.log('⚙️   Configuring sync settings...');
      const syncSettings = await promptForSyncSettings();
      console.log();

      // Step 6: Build and write configuration
      const config: PostmanSyncConfig = {
        postman: {
          apiKey: '${POSTMAN_API_KEY}',
          collectionId: collection.uid,
          workspaceId: workspace.id,
          baseUrl: 'https://api.postman.com',
        },
        nestjs: {
          projectRoot: nestjsSettings.projectRoot,
          autoDiscovery: nestjsSettings.autoDiscovery,
          srcDirs: nestjsSettings.srcDirs,
          controllersPattern: ['**/*.controller.ts'],
          excludePatterns: [
            '**/node_modules/**',
            '**/dist/**',
            '**/build/**',
            '**/.git/**',
            '**/*.spec.ts',
            '**/*.test.ts',
            '**/coverage/**',
            '**/tmp/**',
            '**/temp/**'
          ],
          globalPrefix: nestjsSettings.globalPrefix,
          autoDetectGlobalPrefix: nestjsSettings.autoDetectGlobalPrefix,
          isMicroservice: nestjsSettings.isMicroservice,
          ...(nestjsSettings.microservicePrefixes && { microservicePrefixes: nestjsSettings.microservicePrefixes }),
        },
        sync: {
          outputFile: syncSettings.outputFile,
          watchMode: syncSettings.watchMode,
          watchPaths: ['src/**/*.ts'],
          debounceMs: 1000,
        },
        options: {
          generateDocs: syncSettings.generateDocs,
          includePrivate: syncSettings.includePrivate,
          includeDeprecated: syncSettings.includeDeprecated,
          overwriteExisting: true,
        },
      };

      console.log('💾  Saving configuration...');
      await writeConfigFile(config);

      // Success output
      console.log('\n✅  Configuration saved to postman.config.json');
      console.log(`✅  Workspace: ${workspace.name} (${workspace.id})`);
      console.log(`✅  Collection: ${collection.name} (${collection.uid})`);
      console.log(`✅  Project Root: ${nestjsSettings.projectRoot}`);
      console.log(`✅  Auto-Discovery: ${nestjsSettings.autoDiscovery ? 'Enabled' : 'Disabled'}`);
      console.log(`✅  Architecture: ${nestjsSettings.isMicroservice ? 'Microservice' : 'Monolithic'}`);

      if (nestjsSettings.isMicroservice) {
        console.log(`✅  Auto-Detect Prefixes: ${nestjsSettings.autoDetectGlobalPrefix ? 'Enabled' : 'Disabled'}`);
        if (nestjsSettings.microservicePrefixes && Object.keys(nestjsSettings.microservicePrefixes).length > 0) {
          console.log('✅  Configured Microservice Prefixes:');
          Object.entries(nestjsSettings.microservicePrefixes).forEach(([service, prefix]) => {
            console.log(`     • ${service}: ${prefix || '(no prefix)'}`);
          });
        } else {
          console.log('✅  Microservice Prefixes: Will be auto-detected during generation');
        }
      } else {
        console.log(`✅  Global Prefix: ${nestjsSettings.globalPrefix || 'None'}`);
      }

      console.log('\n🚀  Next steps:');
      console.log('   1. Run `nest-postman-sync generate` to create your collection');
      console.log('   2. Run `nest-postman-sync sync` to push to Postman');
      logger.info('Init command completed successfully', { module: 'init' });
    } catch (error) {
      logger.error('Init command failed', error as Error, { module: 'init' });
      if (error instanceof PostmanError) {
        const meta = error.meta || {};
        const method = meta.method || '';
        const url = meta.url || '';
        const status = meta.status || '';
        const message = error.message || '';
        console.error(`❌  [postman] ${method} ${url} – ${status} : ${message}`);
        if (process.argv.includes('--debug')) {
          console.error(error.stack);
        }
      } else {
        console.error(`❌  An unexpected error occurred: ${(error as Error).message}`);
        if (process.argv.includes('--debug')) {
          console.error((error as Error).stack);
        }
      }
      process.exit(1);
    }
  });
