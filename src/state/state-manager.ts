import * as fs from 'fs-extra';
import * as path from 'path';
import { logger } from '@/utils/logger';

export interface SyncState {
  lastSync: string;
  collectionId?: string;
  workspaceId?: string;
  collections: CollectionState[];
  files: FileState[];
  checksums: Record<string, string>;
}

export interface CollectionState {
  id: string;
  name: string;
  lastSync: string;
  lastUpdated?: string;
  checksum?: string;
  endpointCount?: number;
}

export interface FileState {
  path: string;
  lastModified: string;
  checksum: string;
}

export class StateManager {
  private static instance: StateManager;
  private statePath: string;
  private state: SyncState;

  constructor(statePath = 'state.json') {
    this.statePath = path.resolve(statePath);
    this.state = this.getDefaultState();
  }

  static getInstance(statePath = 'state.json'): StateManager {
    if (!StateManager.instance) {
      StateManager.instance = new StateManager(statePath);
    }
    return StateManager.instance;
  }

  /**
   * Load state from file
   */
  async load(): Promise<SyncState> {
    try {
      if (await fs.pathExists(this.statePath)) {
        logger.debug(`Loading state from: ${this.statePath}`, { module: 'state' });
        this.state = await fs.readJson(this.statePath);
        logger.debug('State loaded successfully', { module: 'state' });
      } else {
        logger.debug('No existing state file found, using default state', { module: 'state' });
        await this.save();
      }
      return this.state;
    } catch (error) {
      logger.error('Failed to load state', error as Error, { module: 'state' });
      this.state = this.getDefaultState();
      return this.state;
    }
  }

  /**
   * Save state to file
   */
  async save(): Promise<void> {
    try {
      logger.debug(`Saving state to: ${this.statePath}`, { module: 'state' });
      await fs.writeJson(this.statePath, this.state, { spaces: 2 });
      logger.debug('State saved successfully', { module: 'state' });
    } catch (error) {
      logger.error('Failed to save state', error as Error, { module: 'state' });
      throw error;
    }
  }

  /**
   * Get current state
   */
  getState(): SyncState {
    return { ...this.state };
  }

  /**
   * Update last sync timestamp
   */
  updateLastSync(): void {
    this.state.lastSync = new Date().toISOString();
    logger.debug('Updated last sync timestamp', { module: 'state' });
  }

  /**
   * Set collection ID
   */
  setCollectionId(collectionId: string): void {
    this.state.collectionId = collectionId;
    logger.debug(`Set collection ID: ${collectionId}`, { module: 'state' });
  }

  /**
   * Set workspace ID
   */
  setWorkspaceId(workspaceId: string): void {
    this.state.workspaceId = workspaceId;
    logger.debug(`Set workspace ID: ${workspaceId}`, { module: 'state' });
  }

  /**
   * Add or update collection state
   */
  updateCollectionState(collection: CollectionState): void {
    const existingIndex = this.state.collections.findIndex(c => c.id === collection.id);
    
    if (existingIndex >= 0) {
      this.state.collections[existingIndex] = collection;
      logger.debug(`Updated collection state: ${collection.name}`, { module: 'state' });
    } else {
      this.state.collections.push(collection);
      logger.debug(`Added new collection state: ${collection.name}`, { module: 'state' });
    }
  }

  /**
   * Update file state
   */
  updateFileState(filePath: string, checksum: string): void {
    const normalizedPath = path.normalize(filePath);
    const existingIndex = this.state.files.findIndex(f => f.path === normalizedPath);
    
    const fileState: FileState = {
      path: normalizedPath,
      lastModified: new Date().toISOString(),
      checksum,
    };

    if (existingIndex >= 0) {
      this.state.files[existingIndex] = fileState;
    } else {
      this.state.files.push(fileState);
    }

    // Also update checksums map for quick lookup
    this.state.checksums[normalizedPath] = checksum;
    
    logger.debug(`Updated file state: ${normalizedPath}`, { module: 'state' });
  }

  /**
   * Check if file has changed since last sync
   */
  hasFileChanged(filePath: string, currentChecksum: string): boolean {
    const normalizedPath = path.normalize(filePath);
    const lastChecksum = this.state.checksums[normalizedPath];
    
    const hasChanged = !lastChecksum || lastChecksum !== currentChecksum;
    
    if (hasChanged) {
      logger.debug(`File changed: ${normalizedPath}`, { module: 'state' });
    }
    
    return hasChanged;
  }

  /**
   * Get files that have changed since last sync
   */
  getChangedFiles(currentChecksums: Record<string, string>): string[] {
    const changedFiles: string[] = [];

    for (const [filePath, checksum] of Object.entries(currentChecksums)) {
      if (this.hasFileChanged(filePath, checksum)) {
        changedFiles.push(filePath);
      }
    }

    logger.debug(`Found ${changedFiles.length} changed files`, { module: 'state' });
    return changedFiles;
  }

  /**
   * Remove file state
   */
  removeFileState(filePath: string): void {
    const normalizedPath = path.normalize(filePath);
    
    this.state.files = this.state.files.filter(f => f.path !== normalizedPath);
    delete this.state.checksums[normalizedPath];
    
    logger.debug(`Removed file state: ${normalizedPath}`, { module: 'state' });
  }

  /**
   * Clear all state
   */
  clear(): void {
    this.state = this.getDefaultState();
    logger.debug('Cleared all state', { module: 'state' });
  }

  /**
   * Get default state structure
   */
  private getDefaultState(): SyncState {
    return {
      lastSync: new Date().toISOString(),
      collections: [],
      files: [],
      checksums: {},
    };
  }

  /**
   * Export state for debugging
   */
  dumpState(): void {
    logger.dumpState(this.state as unknown as Record<string, unknown>, { module: 'state' });
  }
}
