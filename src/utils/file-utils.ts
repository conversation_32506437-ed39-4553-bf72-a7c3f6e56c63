import * as fs from 'fs-extra';
import * as path from 'path';
import * as crypto from 'crypto';
import { glob } from 'glob';
import { logger } from '@/utils/logger';

export interface FileInfo {
  path: string;
  hash: string;
  lastModified: Date;
  size: number;
}

/**
 * Generate SHA-256 hash for a file
 */
export async function generateFileHash(filePath: string): Promise<string> {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    return crypto.createHash('sha256').update(content).digest('hex');
  } catch (error) {
    logger.error(`Failed to generate hash for file: ${filePath}`, error as Error, { module: 'file-utils' });
    throw error;
  }
}

/**
 * Generate hashes for multiple files
 */
export async function generateFileHashes(filePaths: string[]): Promise<Record<string, string>> {
  const hashes: Record<string, string> = {};
  
  await Promise.all(
    filePaths.map(async (filePath) => {
      try {
        hashes[filePath] = await generateFileHash(filePath);
      } catch (error) {
        logger.warn(`Skipping file due to hash generation error: ${filePath}`, { module: 'file-utils' });
      }
    })
  );
  
  return hashes;
}

/**
 * Smart controller discovery - searches entire project for controllers
 */
export async function findControllerFilesAuto(
  projectRoot: string = '.',
  excludePatterns: string[] = [
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',
    '**/.git/**',
    '**/*.spec.ts',
    '**/*.test.ts',
    '**/coverage/**',
    '**/tmp/**',
    '**/temp/**'
  ]
): Promise<string[]> {
  logger.debug(`Auto-discovering controller files in: ${projectRoot}`, { module: 'file-utils' });

  try {
    // Search for all .controller.ts files in the entire project
    const pattern = path.join(projectRoot, '**/*.controller.ts');
    const files = await glob(pattern, {
      ignore: excludePatterns,
      absolute: true,
    });

    // Remove duplicates and sort
    const uniqueFiles = [...new Set(files)].sort();

    logger.info(`Auto-discovered ${uniqueFiles.length} controller files`, { module: 'file-utils' });

    // Log discovered directories for user information
    const directories = [...new Set(uniqueFiles.map(file => path.dirname(file)))];
    logger.debug(`Controllers found in directories: ${directories.join(', ')}`, { module: 'file-utils' });

    return uniqueFiles;
  } catch (error) {
    logger.error('Failed to auto-discover controller files', error as Error, { module: 'file-utils' });
    throw error;
  }
}

/**
 * Find controller files using specific directories (manual configuration)
 */
export async function findControllerFiles(
  srcDirs: string | string[],
  patterns: string[] = ['**/*.controller.ts'],
  excludePatterns: string[] = ['**/*.spec.ts', '**/*.test.ts']
): Promise<string[]> {
  const directories = Array.isArray(srcDirs) ? srcDirs : [srcDirs];
  logger.debug(`Finding controller files in directories: ${directories.join(', ')}`, { module: 'file-utils' });

  try {
    const allFiles: string[] = [];

    // Search in each specified directory
    for (const srcDir of directories) {
      if (!(await isDirectoryAccessible(srcDir))) {
        logger.warn(`Directory not accessible, skipping: ${srcDir}`, { module: 'file-utils' });
        continue;
      }

      // Find files matching include patterns
      for (const pattern of patterns) {
        const fullPattern = path.join(srcDir, pattern);
        const files = await glob(fullPattern, {
          ignore: excludePatterns.map(exclude => path.join(srcDir, exclude)),
          absolute: true,
        });
        allFiles.push(...files);
      }
    }

    // Remove duplicates and sort
    const uniqueFiles = [...new Set(allFiles)].sort();

    logger.debug(`Found ${uniqueFiles.length} controller files in specified directories`, { module: 'file-utils' });
    return uniqueFiles;
  } catch (error) {
    logger.error('Failed to find controller files', error as Error, { module: 'file-utils' });
    throw error;
  }
}

/**
 * Get file information including hash and metadata
 */
export async function getFileInfo(filePath: string): Promise<FileInfo> {
  try {
    const stats = await fs.stat(filePath);
    const hash = await generateFileHash(filePath);
    
    return {
      path: path.normalize(filePath),
      hash,
      lastModified: stats.mtime,
      size: stats.size,
    };
  } catch (error) {
    logger.error(`Failed to get file info for: ${filePath}`, error as Error, { module: 'file-utils' });
    throw error;
  }
}

/**
 * Get file information for multiple files
 */
export async function getFilesInfo(filePaths: string[]): Promise<FileInfo[]> {
  const filesInfo: FileInfo[] = [];
  
  await Promise.all(
    filePaths.map(async (filePath) => {
      try {
        const fileInfo = await getFileInfo(filePath);
        filesInfo.push(fileInfo);
      } catch (error) {
        logger.warn(`Skipping file due to info extraction error: ${filePath}`, { module: 'file-utils' });
      }
    })
  );
  
  return filesInfo.sort((a, b) => a.path.localeCompare(b.path));
}

/**
 * Check if a file exists and is readable
 */
export async function isFileAccessible(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath, fs.constants.R_OK);
    return true;
  } catch {
    return false;
  }
}

/**
 * Check if a directory exists and is readable
 */
export async function isDirectoryAccessible(dirPath: string): Promise<boolean> {
  try {
    const stats = await fs.stat(dirPath);
    return stats.isDirectory();
  } catch {
    return false;
  }
}

/**
 * Ensure directory exists
 */
export async function ensureDirectory(dirPath: string): Promise<void> {
  try {
    await fs.ensureDir(dirPath);
  } catch (error) {
    logger.error(`Failed to ensure directory: ${dirPath}`, error as Error, { module: 'file-utils' });
    throw error;
  }
}

/**
 * Write file with directory creation
 */
export async function writeFileWithDir(filePath: string, content: string): Promise<void> {
  try {
    await ensureDirectory(path.dirname(filePath));
    await fs.writeFile(filePath, content, 'utf8');
  } catch (error) {
    logger.error(`Failed to write file: ${filePath}`, error as Error, { module: 'file-utils' });
    throw error;
  }
}

/**
 * Compare file hashes to detect changes
 */
export function compareFileHashes(
  currentHashes: Record<string, string>,
  previousHashes: Record<string, string>
): {
  added: string[];
  modified: string[];
  deleted: string[];
  unchanged: string[];
} {
  const currentFiles = new Set(Object.keys(currentHashes));
  const previousFiles = new Set(Object.keys(previousHashes));
  
  const added = [...currentFiles].filter(file => !previousFiles.has(file));
  const deleted = [...previousFiles].filter(file => !currentFiles.has(file));
  const modified = [...currentFiles].filter(file => 
    previousFiles.has(file) && currentHashes[file] !== previousHashes[file]
  );
  const unchanged = [...currentFiles].filter(file =>
    previousFiles.has(file) && currentHashes[file] === previousHashes[file]
  );
  
  return { added, modified, deleted, unchanged };
}
