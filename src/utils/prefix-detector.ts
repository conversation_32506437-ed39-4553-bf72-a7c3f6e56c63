import * as fs from 'fs-extra';
import * as path from 'path';
import { Project, SourceFile } from 'ts-morph';
import { logger } from '@/utils/logger';

export interface GlobalPrefixDetectionResult {
  prefix: string;
  source: 'detected' | 'config' | 'default';
  confidence: 'high' | 'medium' | 'low';
  detectedFrom?: string;
  microservicePrefixes?: Record<string, string>;
  architecture: 'monolithic' | 'microservice' | 'mixed';
}

export interface ControllerPrefixMapping {
  controllerPath: string;
  serviceName: string;
  servicePrefix: string;
}

export class GlobalPrefixDetector {
  private project: Project;

  constructor(projectRoot: string) {
    this.project = new Project({
      tsConfigFilePath: path.join(projectRoot, 'tsconfig.json'),
      skipAddingFilesFromTsConfig: true,
      useInMemoryFileSystem: false,
    });
  }

  /**
   * Detect global prefix from NestJS application
   */
  async detectGlobalPrefix(
    projectRoot: string,
    configuredPrefix?: string
  ): Promise<GlobalPrefixDetectionResult> {
    logger.debug('Starting comprehensive global prefix detection', { module: 'prefix-detector' });

    // If explicitly configured, use that
    if (configuredPrefix !== undefined && configuredPrefix !== '') {
      logger.debug(`Using configured prefix: ${configuredPrefix}`, { module: 'prefix-detector' });
      return {
        prefix: configuredPrefix,
        source: 'config',
        confidence: 'high',
        architecture: 'monolithic', // Assume monolithic when manually configured
      };
    }

    // Detect architecture and prefixes
    const architectureResult = await this.detectArchitectureAndPrefixes(projectRoot);

    if (architectureResult.microservicePrefixes && Object.keys(architectureResult.microservicePrefixes).length > 0) {
      return architectureResult;
    }

    // Try to detect from main.ts or app.ts files (fallback for monolithic)
    const detectedPrefix = await this.detectFromMainFiles(projectRoot);
    if (detectedPrefix) {
      return {
        ...detectedPrefix,
        architecture: 'monolithic',
      };
    }

    // Try to detect from common patterns in controllers
    const controllerPrefix = await this.detectFromControllerPatterns(projectRoot);
    if (controllerPrefix) {
      return {
        ...controllerPrefix,
        architecture: 'monolithic',
      };
    }

    // Default fallback
    logger.debug('No global prefix detected, using empty prefix', { module: 'prefix-detector' });
    return {
      prefix: '',
      source: 'default',
      confidence: 'low',
      architecture: 'monolithic',
    };
  }

  /**
   * Detect architecture and prefixes comprehensively
   */
  private async detectArchitectureAndPrefixes(projectRoot: string): Promise<GlobalPrefixDetectionResult> {
    logger.debug('Analyzing NestJS application architecture', { module: 'prefix-detector' });

    // Find all main.ts files to determine architecture
    const mainFiles = await this.findMainFiles(projectRoot);
    const microservicePrefixes: Record<string, string> = {};
    let globalPrefix = '';
    let architecture: 'monolithic' | 'microservice' | 'mixed' = 'monolithic';

    if (mainFiles.length > 1) {
      architecture = 'microservice';
      logger.debug(`Detected microservice architecture with ${mainFiles.length} main files`, { module: 'prefix-detector' });

      // Analyze each microservice
      for (const mainFile of mainFiles) {
        const serviceName = this.extractServiceName(mainFile);
        const prefix = await this.extractPrefixFromMainFile(mainFile);

        if (prefix) {
          microservicePrefixes[serviceName] = prefix;
          logger.debug(`Service "${serviceName}" has prefix: ${prefix}`, { module: 'prefix-detector' });
        }
      }

      // If all services have the same prefix, use it as global
      const uniquePrefixes = [...new Set(Object.values(microservicePrefixes))];
      if (uniquePrefixes.length === 1 && uniquePrefixes[0]) {
        globalPrefix = uniquePrefixes[0];
        architecture = 'mixed'; // Same prefix across services
      }
    } else if (mainFiles.length === 1 && mainFiles[0]) {
      // Single main file - could be monolithic or gateway
      const prefix = await this.extractPrefixFromMainFile(mainFiles[0]);
      if (prefix) {
        globalPrefix = prefix;
      }
    }

    // If no global prefix found, try to detect common patterns
    if (!globalPrefix && Object.keys(microservicePrefixes).length === 0) {
      const controllerAnalysis = await this.analyzeControllerPrefixes(projectRoot);
      if (controllerAnalysis.commonPrefix) {
        globalPrefix = controllerAnalysis.commonPrefix;
      }
    }

    const result: GlobalPrefixDetectionResult = {
      prefix: globalPrefix,
      source: globalPrefix ? 'detected' : 'default',
      confidence: this.calculateConfidence(globalPrefix, microservicePrefixes, mainFiles.length),
      detectedFrom: mainFiles.length > 1 ? 'microservice-analysis' : 'main-file-analysis',
      architecture,
    };

    if (Object.keys(microservicePrefixes).length > 0) {
      result.microservicePrefixes = microservicePrefixes;
    }

    return result;
  }

  /**
   * Find all main.ts files in the project
   */
  private async findMainFiles(projectRoot: string): Promise<string[]> {
    const { glob } = require('glob');
    const patterns = [
      '**/main.ts',
      '**/app.ts',
      '**/bootstrap.ts',
    ];

    const allFiles: string[] = [];

    for (const pattern of patterns) {
      try {
        const files: string[] = await glob(path.join(projectRoot, pattern), {
          ignore: [
            '**/node_modules/**',
            '**/dist/**',
            '**/build/**',
            '**/.git/**',
            '**/test/**',
            '**/tests/**',
          ],
          absolute: true,
        });
        allFiles.push(...files);
      } catch (error) {
        logger.warn(`Failed to glob pattern ${pattern}`, { module: 'prefix-detector' });
      }
    }

    // Remove duplicates and sort
    return [...new Set(allFiles)].sort();
  }

  /**
   * Extract service name from main file path
   */
  private extractServiceName(mainFilePath: string): string {
    const parts = mainFilePath.split(path.sep);

    // Look for patterns like apps/service-name/src/main.ts
    const appsIndex = parts.findIndex(part => part === 'apps');
    if (appsIndex !== -1 && appsIndex + 1 < parts.length) {
      return parts[appsIndex + 1] || 'unknown';
    }

    // Look for patterns like src/service-name/main.ts
    const srcIndex = parts.findIndex(part => part === 'src');
    if (srcIndex !== -1 && srcIndex + 1 < parts.length && parts[srcIndex + 1] !== 'main.ts') {
      return parts[srcIndex + 1] || 'unknown';
    }

    // Fallback to parent directory name
    return parts[parts.length - 2] || 'unknown';
  }

  /**
   * Analyze controller prefixes to find common patterns
   */
  private async analyzeControllerPrefixes(projectRoot: string): Promise<{ commonPrefix?: string }> {
    try {
      const controllerFiles = await this.findControllerFiles(projectRoot);
      const prefixCounts = new Map<string, number>();

      for (const filePath of controllerFiles.slice(0, 20)) { // Analyze first 20 controllers
        try {
          const sourceFile = this.project.addSourceFileAtPath(filePath);
          const classes = sourceFile.getClasses();

          for (const cls of classes) {
            const controllerDecorator = cls.getDecorators().find(d => d.getName() === 'Controller');
            if (controllerDecorator) {
              const args = controllerDecorator.getArguments();
              if (args.length > 0) {
                const pathArg = args[0];
                if (pathArg) {
                  const controllerPath = pathArg.getText().slice(1, -1); // Remove quotes

                  // Extract potential global prefix patterns
                  const segments = controllerPath.split('/').filter(s => s.length > 0);
                  if (segments.length > 0) {
                    const firstSegment = `/${segments[0]}`;
                    if (this.isLikelyGlobalPrefix(firstSegment)) {
                      prefixCounts.set(firstSegment, (prefixCounts.get(firstSegment) || 0) + 1);
                    }
                  }
                }
              }
            }
          }
        } catch (error) {
          // Skip files that can't be parsed
          continue;
        }
      }

      // Find the most common prefix
      if (prefixCounts.size > 0) {
        const sortedPrefixes = Array.from(prefixCounts.entries())
          .sort((a, b) => b[1] - a[1]);

        if (sortedPrefixes.length > 0) {
          const firstEntry = sortedPrefixes[0];
          if (firstEntry) {
            const mostCommonPrefix = firstEntry[0];
            const count = firstEntry[1];

            if (count >= 3) { // Only consider if found in multiple controllers
              logger.debug(`Detected common controller prefix: ${mostCommonPrefix} (${count} occurrences)`, { module: 'prefix-detector' });
              return { commonPrefix: mostCommonPrefix };
            }
          }
        }
      }
    } catch (error) {
      logger.warn('Failed to analyze controller prefixes', { module: 'prefix-detector' });
    }

    return {};
  }

  /**
   * Extract prefix from a specific main file
   */
  private async extractPrefixFromMainFile(mainFilePath: string): Promise<string | null> {
    try {
      const sourceFile = this.project.addSourceFileAtPath(mainFilePath);
      return this.extractGlobalPrefixFromFile(sourceFile);
    } catch (error) {
      logger.warn(`Failed to parse ${mainFilePath}`, { module: 'prefix-detector' });
      return null;
    }
  }

  /**
   * Calculate confidence based on detection results
   */
  private calculateConfidence(
    globalPrefix: string,
    microservicePrefixes: Record<string, string>,
    mainFileCount: number
  ): 'high' | 'medium' | 'low' {
    if (globalPrefix && mainFileCount >= 1) return 'high';
    if (Object.keys(microservicePrefixes).length > 0) return 'medium';
    if (globalPrefix) return 'medium';
    return 'low';
  }

  /**
   * Detect global prefix from main.ts, app.ts, or bootstrap files
   */
  private async detectFromMainFiles(projectRoot: string): Promise<GlobalPrefixDetectionResult | null> {
    const mainFiles = [
      'src/main.ts',
      'src/app.ts',
      'src/bootstrap.ts',
      'main.ts',
      'app.ts',
      'bootstrap.ts',
    ];

    for (const mainFile of mainFiles) {
      const filePath = path.join(projectRoot, mainFile);
      
      if (await fs.pathExists(filePath)) {
        logger.debug(`Checking main file: ${filePath}`, { module: 'prefix-detector' });
        
        try {
          const sourceFile = this.project.addSourceFileAtPath(filePath);
          const prefix = this.extractGlobalPrefixFromFile(sourceFile);
          
          if (prefix) {
            logger.debug(`Detected global prefix "${prefix}" from ${mainFile}`, { module: 'prefix-detector' });
            return {
              prefix,
              source: 'detected',
              confidence: 'high',
              detectedFrom: mainFile,
              architecture: 'monolithic',
            };
          }
        } catch (error) {
          logger.warn(`Failed to parse ${mainFile}`, { module: 'prefix-detector' });
        }
      }
    }

    return null;
  }

  /**
   * Extract global prefix from a source file
   */
  private extractGlobalPrefixFromFile(sourceFile: SourceFile): string | null {
    const text = sourceFile.getFullText();

    // Look for app.setGlobalPrefix() calls
    const globalPrefixRegex = /\.setGlobalPrefix\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
    let match;
    
    while ((match = globalPrefixRegex.exec(text)) !== null) {
      const prefix = match[1];
      if (prefix) {
        return prefix.startsWith('/') ? prefix : `/${prefix}`;
      }
    }

    // Look for ConfigService.get() calls that might contain API prefix
    const configRegex = /configService\.get\s*\(\s*['"`]([^'"`]*(?:api|prefix|base)[^'"`]*)['"`]\s*\)/gi;
    while ((match = configRegex.exec(text)) !== null) {
      const configKey = match[1];
      if (configKey && (configKey.toLowerCase().includes('prefix') || configKey.toLowerCase().includes('api'))) {
        // This is a potential prefix from config, but we can't determine the actual value
        // Return a common default
        return '/api';
      }
    }

    // Look for environment variable usage
    const envRegex = /process\.env\[?['"`]([^'"`]*(?:API|PREFIX|BASE)[^'"`]*)['"`]\]?/gi;
    while ((match = envRegex.exec(text)) !== null) {
      const envKey = match[1];
      if (envKey && (envKey.toLowerCase().includes('prefix') || envKey.toLowerCase().includes('api'))) {
        return '/api'; // Common default
      }
    }

    return null;
  }

  /**
   * Detect common prefix patterns from controller analysis
   */
  private async detectFromControllerPatterns(projectRoot: string): Promise<GlobalPrefixDetectionResult | null> {
    try {
      // Look for common controller patterns
      const controllerFiles = await this.findControllerFiles(projectRoot);
      const prefixCounts = new Map<string, number>();

      for (const filePath of controllerFiles.slice(0, 10)) { // Analyze first 10 controllers
        try {
          const sourceFile = this.project.addSourceFileAtPath(filePath);
          const classes = sourceFile.getClasses();

          for (const cls of classes) {
            const controllerDecorator = cls.getDecorators().find(d => d.getName() === 'Controller');
            if (controllerDecorator) {
              const args = controllerDecorator.getArguments();
              if (args.length > 0) {
                const pathArg = args[0];
                if (pathArg) {
                  const controllerPath = pathArg.getText().slice(1, -1); // Remove quotes

                  // Extract potential global prefix patterns
                  const segments = controllerPath.split('/').filter(s => s.length > 0);
                  if (segments.length > 0) {
                    const firstSegment = `/${segments[0]}`;
                    if (this.isLikelyGlobalPrefix(firstSegment)) {
                      prefixCounts.set(firstSegment, (prefixCounts.get(firstSegment) || 0) + 1);
                    }
                  }
                }
              }
            }
          }
        } catch (error) {
          // Skip files that can't be parsed
          continue;
        }
      }

      // Find the most common prefix
      if (prefixCounts.size > 0) {
        const sortedPrefixes = Array.from(prefixCounts.entries())
          .sort((a, b) => b[1] - a[1]);

        if (sortedPrefixes.length > 0) {
          const firstEntry = sortedPrefixes[0];
          if (firstEntry) {
            const mostCommonPrefix = firstEntry[0];
            const count = firstEntry[1];
            const confidence = count >= 3 ? 'medium' : 'low';

          logger.debug(`Detected common prefix pattern: ${mostCommonPrefix} (${count} occurrences)`, { module: 'prefix-detector' });

            return {
              prefix: mostCommonPrefix,
              source: 'detected',
              confidence,
              detectedFrom: 'controller-patterns',
              architecture: 'monolithic',
            };
          }
        }
      }
    } catch (error) {
      logger.warn('Failed to analyze controller patterns', { module: 'prefix-detector' });
    }

    return null;
  }

  /**
   * Check if a path segment is likely a global prefix
   */
  private isLikelyGlobalPrefix(segment: string): boolean {
    const commonPrefixes = [
      '/api',
      '/v1',
      '/v2',
      '/v3',
      '/rest',
      '/graphql',
      '/app',
      '/service',
    ];

    return commonPrefixes.includes(segment.toLowerCase()) ||
           segment.match(/^\/v\d+$/) !== null ||
           segment.match(/^\/api/) !== null;
  }

  /**
   * Find controller files in the project
   */
  private async findControllerFiles(projectRoot: string): Promise<string[]> {
    const { glob } = require('glob');
    const pattern = path.join(projectRoot, '**/*.controller.ts');

    try {
      return await glob(pattern, {
        ignore: [
          '**/node_modules/**',
          '**/dist/**',
          '**/build/**',
          '**/.git/**',
          '**/*.spec.ts',
          '**/*.test.ts',
        ],
        absolute: true,
      });
    } catch (error) {
      logger.warn('Failed to find controller files', { module: 'prefix-detector' });
      return [];
    }
  }

  /**
   * Create controller-to-service prefix mappings
   */
  async createControllerPrefixMappings(
    controllerFiles: string[],
    projectRoot: string
  ): Promise<ControllerPrefixMapping[]> {
    const mappings: ControllerPrefixMapping[] = [];

    for (const controllerPath of controllerFiles) {
      const serviceName = this.extractServiceNameFromPath(controllerPath, projectRoot);
      const servicePrefix = this.getServicePrefix(serviceName);

      mappings.push({
        controllerPath,
        serviceName,
        servicePrefix,
      });
    }

    return mappings;
  }

  /**
   * Extract service name from controller file path
   */
  private extractServiceNameFromPath(controllerPath: string, projectRoot: string): string {
    const relativePath = path.relative(projectRoot, controllerPath);
    const parts = relativePath.split(path.sep);

    // Look for apps/service-name pattern
    const appsIndex = parts.findIndex(part => part === 'apps');
    if (appsIndex !== -1 && appsIndex + 1 < parts.length) {
      return parts[appsIndex + 1] || 'unknown';
    }

    // Look for libs/service-name pattern
    const libsIndex = parts.findIndex(part => part === 'libs');
    if (libsIndex !== -1 && libsIndex + 1 < parts.length) {
      return parts[libsIndex + 1] || 'unknown';
    }

    // Look for src/service-name pattern
    const srcIndex = parts.findIndex(part => part === 'src');
    if (srcIndex !== -1 && srcIndex + 1 < parts.length && parts[srcIndex + 1] !== 'controllers') {
      return parts[srcIndex + 1] || 'unknown';
    }

    // Default to 'core' for root-level controllers
    return 'core';
  }

  /**
   * Get service prefix based on service name using common NestJS patterns
   */
  private getServicePrefix(serviceName: string): string {
    // Common microservice prefix mappings
    const prefixMappings: Record<string, string> = {
      'auth': '/auth',
      'authentication': '/auth',
      'user': '/auth',
      'users': '/auth',

      'case-management': '/case-management',
      'cases': '/case-management',
      'case': '/case-management',

      'task-management': '/task-management',
      'tasks': '/task-management',
      'task': '/task-management',

      'document-engine': '/document-engine',
      'documents': '/document-engine',
      'document': '/document-engine',
      'files': '/document-engine',

      'communication': '/communication',
      'notifications': '/communication',
      'messaging': '/communication',

      'core': '', // Core service usually has no prefix
      'api': '/api',
      'gateway': '', // API gateway usually has no prefix
      'common': '', // Common/shared services usually have no prefix

      // Health endpoints are usually service-specific
      'health': '', // Will be handled by service-specific detection
    };

    // Direct mapping
    if (prefixMappings[serviceName]) {
      return prefixMappings[serviceName];
    }

    // Pattern-based mapping
    if (serviceName.includes('auth')) return '/auth';
    if (serviceName.includes('case')) return '/case-management';
    if (serviceName.includes('task')) return '/task-management';
    if (serviceName.includes('document') || serviceName.includes('file')) return '/document-engine';
    if (serviceName.includes('communication') || serviceName.includes('notification')) return '/communication';

    // Default: use service name as prefix
    return `/${serviceName}`;
  }

  /**
   * Clean up resources
   */
  dispose(): void {
    // Clean up ts-morph project
    this.project.getSourceFiles().forEach(sf => sf.forget());
  }
}

/**
 * Convenience function to detect global prefix
 */
export async function detectGlobalPrefix(
  projectRoot: string,
  configuredPrefix?: string
): Promise<GlobalPrefixDetectionResult> {
  const detector = new GlobalPrefixDetector(projectRoot);
  try {
    return await detector.detectGlobalPrefix(projectRoot, configuredPrefix);
  } finally {
    detector.dispose();
  }
}

/**
 * Convenience function to create controller prefix mappings
 */
export async function createControllerPrefixMappings(
  controllerFiles: string[],
  projectRoot: string
): Promise<ControllerPrefixMapping[]> {
  const detector = new GlobalPrefixDetector(projectRoot);
  try {
    return await detector.createControllerPrefixMappings(controllerFiles, projectRoot);
  } finally {
    detector.dispose();
  }
}
