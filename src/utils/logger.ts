import chalk from 'chalk';
import * as fs from 'fs-extra';
import * as path from 'path';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4,
}

export interface LogOptions {
  module?: string;
  context?: Record<string, unknown>;
}

export interface LoggerConfig {
  level: LogLevel;
  enableColors: boolean;
  enableTimestamps: boolean;
  outputFile?: string;
  debugMode: boolean;
}

class Logger {
  private config: LoggerConfig = {
    level: LogLevel.INFO,
    enableColors: true,
    enableTimestamps: true,
    debugMode: false,
  };

  private outputStream?: fs.WriteStream;

  constructor(config?: Partial<LoggerConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }

    // Set up global error handlers
    this.setupGlobalErrorHandlers();
  }

  /**
   * Set the minimum log level
   */
  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  /**
   * Enable or disable debug mode
   */
  setDebugMode(enabled: boolean): void {
    this.config.debugMode = enabled;
    if (enabled) {
      this.config.level = LogLevel.DEBUG;
    }
  }

  /**
   * Enable quiet mode (only warn, error, fatal)
   */
  setQuietMode(enabled: boolean): void {
    if (enabled) {
      this.config.level = LogLevel.WARN;
    }
  }

  /**
   * Enable verbose mode (includes debug logs)
   */
  setVerboseMode(enabled: boolean): void {
    if (enabled) {
      this.config.level = LogLevel.DEBUG;
    }
  }

  /**
   * Enable file logging
   */
  toFile(filePath: string): void {
    const dir = path.dirname(filePath);
    fs.ensureDirSync(dir);
    
    this.outputStream = fs.createWriteStream(filePath, { flags: 'a' });
    this.config.outputFile = filePath;
  }

  /**
   * Format timestamp
   */
  private formatTimestamp(): string {
    return new Date().toISOString();
  }

  /**
   * Format log level with colors
   */
  private formatLevel(level: LogLevel): string {
    const levelName = LogLevel[level].padEnd(5);
    
    if (!this.config.enableColors) {
      return levelName;
    }

    switch (level) {
      case LogLevel.DEBUG:
        return chalk.gray(levelName);
      case LogLevel.INFO:
        return chalk.blue(levelName);
      case LogLevel.WARN:
        return chalk.yellow(levelName);
      case LogLevel.ERROR:
        return chalk.red(levelName);
      case LogLevel.FATAL:
        return chalk.red.bold(levelName);
      default:
        return levelName;
    }
  }

  /**
   * Format context tag
   */
  private formatContext(options?: LogOptions): string {
    if (!options?.module) return '';
    
    const tag = `[${options.module}]`;
    return this.config.enableColors ? chalk.cyan(tag) : tag;
  }

  /**
   * Build log message
   */
  private buildMessage(level: LogLevel, message: string, options?: LogOptions): string {
    const parts: string[] = [];

    if (this.config.enableTimestamps) {
      parts.push(this.formatTimestamp());
    }

    parts.push(this.formatLevel(level));

    const contextTag = this.formatContext(options);
    if (contextTag) {
      parts.push(contextTag);
    }

    parts.push(message);

    let logMessage = parts.join(' ');

    // Add context data if provided and in debug mode
    if (this.config.debugMode && options?.context) {
      logMessage += `\\n${JSON.stringify(options.context, null, 2)}`;
    }

    return logMessage;
  }

  /**
   * Write log message to outputs
   */
  private writeLog(level: LogLevel, message: string): void {
    const isError = level >= LogLevel.ERROR;
    const stream = isError ? process.stderr : process.stdout;

    // Console output
    stream.write(message + '\\n');

    // File output (without colors)
    if (this.outputStream) {
      const plainMessage = message.replace(
        // eslint-disable-next-line no-control-regex
        /\\u001b\\[[0-9;]*m/g,
        ''
      );
      this.outputStream.write(plainMessage + '\\n');
    }
  }

  /**
   * Core logging method
   */
  private log(level: LogLevel, message: string, options?: LogOptions): void {
    if (level < this.config.level) {
      return;
    }

    const formattedMessage = this.buildMessage(level, message, options);
    this.writeLog(level, formattedMessage);
  }

  /**
   * Debug logging
   */
  debug(message: string, options?: LogOptions): void {
    this.log(LogLevel.DEBUG, message, options);
  }

  /**
   * Info logging
   */
  info(message: string, options?: LogOptions): void {
    this.log(LogLevel.INFO, message, options);
  }

  /**
   * Warning logging
   */
  warn(message: string, options?: LogOptions): void {
    this.log(LogLevel.WARN, message, options);
  }

  /**
   * Error logging
   */
  error(message: string, error?: Error, options?: LogOptions): void {
    let errorMessage = message;
    
    if (error) {
      errorMessage += `: ${error.message}`;
      
      if (this.config.debugMode && error.stack) {
        errorMessage += `\\n${error.stack}`;
      }
    }

    this.log(LogLevel.ERROR, errorMessage, options);
  }

  /**
   * Fatal logging (will exit process)
   */
  fatal(message: string, error?: Error, options?: LogOptions): never {
    this.error(message, error, options);
    process.exit(1);
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    process.on('uncaughtException', (error: Error) => {
      this.fatal('Uncaught exception', error, { module: 'global' });
    });

    process.on('unhandledRejection', (reason: unknown) => {
      const error = reason instanceof Error ? reason : new Error(String(reason));
      this.fatal('Unhandled promise rejection', error, { module: 'global' });
    });

    // Graceful shutdown
    process.on('SIGINT', () => {
      this.info('Received SIGINT, shutting down gracefully...', { module: 'global' });
      this.cleanup();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      this.info('Received SIGTERM, shutting down gracefully...', { module: 'global' });
      this.cleanup();
      process.exit(0);
    });
  }

  /**
   * Cleanup resources
   */
  private cleanup(): void {
    if (this.outputStream) {
      this.outputStream.end();
    }
  }

  /**
   * Log state dump for debugging
   */
  dumpState(state: Record<string, unknown>, options?: LogOptions): void {
    if (this.config.debugMode) {
      this.debug('State dump:', { ...options, context: state });
    }
  }
}

// Create singleton instance
export const logger = new Logger();

// Export the class for custom instances
export { Logger };

// Convenience function to configure logger from CLI flags
export function configureLogger(options: {
  quiet?: boolean;
  verbose?: boolean;
  debug?: boolean;
  logFile?: string;
}): void {
  if (options.quiet) {
    logger.setQuietMode(true);
  } else if (options.verbose) {
    logger.setVerboseMode(true);
  }

  if (options.debug) {
    logger.setDebugMode(true);
  }

  if (options.logFile) {
    logger.toFile(options.logFile);
  }
}
